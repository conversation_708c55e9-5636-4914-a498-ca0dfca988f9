# Design Document

## Overview

The offline playlist feature will add comprehensive playlist management capabilities to the Mhalong chords application. The system will be built using Vue 3 composables for state management, localStorage for data persistence, and will integrate seamlessly with the existing Nuxt 3 architecture. The feature includes playlist creation, song management, sharing via URL parameters, and a dedicated playlist management interface.

## Architecture

### Data Flow
1. **Song Addition**: Users add songs from individual song pages → Playlist composable → localStorage
2. **Playlist Management**: Users manage playlists through dedicated pages → Playlist composable → localStorage  
3. **Sharing**: Generate shareable URLs with song IDs → Route handling → Display shared playlist
4. **Persistence**: All operations immediately sync with localStorage for offline functionality

### Component Hierarchy
```
/playlists (Playlist overview page)
├── PlaylistCard.vue (Individual playlist preview)
└── EmptyPlaylistState.vue (No playlists state)

/playlists/[name] (Individual playlist page)
├── PlaylistHeader.vue (Title, actions, share button)
├── PlaylistSongList.vue (Draggable song list)
│   └── PlaylistSongItem.vue (Individual song with delete)
└── PlaylistActions.vue (Delete playlist, export)

/song/[id] (Enhanced existing page)
└── AddToPlaylistButton.vue (New component)
    └── PlaylistSelector.vue (Dropdown/modal for playlist selection)
```

## Components and Interfaces

### Core Data Types

```typescript
interface PlaylistItem {
  id: string
  name: string
  songIds: string[]
  createdAt: string
  updatedAt: string
}

interface PlaylistSong {
  id: string
  title: string
  artist: string
  artist_slug: string
  thumbnail: string
  key: string
  chords: string[]
}

interface PlaylistState {
  playlists: PlaylistItem[]
  currentPlaylist: PlaylistItem | null
  isLoading: boolean
  error: string | null
}
```

### Composables

#### usePlaylist()
Primary composable for playlist management with the following methods:
- `createPlaylist(name: string): Promise<PlaylistItem>`
- `deletePlaylist(id: string): Promise<void>`
- `addSongToPlaylist(playlistId: string, songId: string): Promise<void>`
- `removeSongFromPlaylist(playlistId: string, songId: string): Promise<void>`
- `reorderPlaylistSongs(playlistId: string, songIds: string[]): Promise<void>`
- `getPlaylist(id: string): PlaylistItem | null`
- `getAllPlaylists(): PlaylistItem[]`
- `generateShareUrl(playlistId: string): string`
- `loadSharedPlaylist(name: string, songIds: string[]): Promise<PlaylistSong[]>`

#### usePlaylistStorage()
Handles localStorage operations:
- `savePlaylist(playlist: PlaylistItem): void`
- `loadPlaylists(): PlaylistItem[]`
- `deletePlaylistFromStorage(id: string): void`
- `isStorageAvailable(): boolean`

### Components Design

#### AddToPlaylistButton.vue
- **Location**: Integrated into `/song/[id].vue` page
- **Functionality**: 
  - Displays "Add to Playlist" button
  - Opens playlist selector on click
  - Shows success/error notifications
- **Props**: `songId: string`
- **State**: Loading state during operations

#### PlaylistSelector.vue
- **Functionality**:
  - Dropdown/modal showing existing playlists
  - "Create New Playlist" option
  - Input field for new playlist name
  - Handles playlist creation and song addition
- **Props**: `songId: string`, `isOpen: boolean`
- **Events**: `close`, `playlistSelected`

#### PlaylistCard.vue
- **Functionality**:
  - Displays playlist name, song count, creation date
  - Click to navigate to playlist detail
  - Quick actions (share, delete)
- **Props**: `playlist: PlaylistItem`

#### PlaylistSongList.vue
- **Functionality**:
  - Draggable list using Vue Draggable Next
  - Reorder songs with immediate persistence
  - Delete songs from playlist
- **Props**: `songs: PlaylistSong[]`, `playlistId: string`
- **Events**: `songReordered`, `songDeleted`

## Data Models

### localStorage Schema
```typescript
// Key: 'mhalong_playlists'
interface StoredData {
  version: string
  playlists: PlaylistItem[]
  lastUpdated: string
}
```

### URL Sharing Format
```
/playlists/[name]?ids=song1,song2,song3
```

### API Integration
The system will use existing song API endpoints:
- `GET /api/songs/[id]` - Fetch individual song data
- Batch loading for multiple songs in shared playlists
- Error handling for invalid/missing song IDs

## Error Handling

### localStorage Errors
- **Storage Unavailable**: Show warning message, disable playlist features
- **Quota Exceeded**: Implement cleanup strategy, show user notification
- **Corrupted Data**: Graceful fallback, offer data reset option

### API Errors
- **Song Not Found**: Skip invalid songs in shared playlists, show notification
- **Network Errors**: Show offline message, retry mechanism
- **Rate Limiting**: Implement request queuing

### User Experience Errors
- **Duplicate Songs**: Prevent adding same song twice, show notification
- **Empty Playlists**: Show empty state with helpful instructions
- **Invalid Share URLs**: Show error page with navigation options

## Testing Strategy

### Unit Tests
- Composable functions (`usePlaylist`, `usePlaylistStorage`)
- Data validation and transformation utilities
- localStorage operations with mocked storage
- URL parameter parsing and generation

### Component Tests
- User interactions (add to playlist, reorder, delete)
- Props and event handling
- Loading and error states
- Drag and drop functionality

### Integration Tests
- End-to-end playlist creation workflow
- Sharing and loading shared playlists
- localStorage persistence across sessions
- Error recovery scenarios

### Manual Testing Scenarios
- Create playlist from song page
- Reorder songs via drag and drop
- Share playlist and access via URL
- Handle storage quota limits
- Test with disabled localStorage
- Verify data persistence across browser sessions

## Performance Considerations

### Optimization Strategies
- **Lazy Loading**: Load song details only when needed
- **Debounced Operations**: Batch localStorage writes
- **Virtual Scrolling**: For large playlists (100+ songs)
- **Caching**: Cache song data to reduce API calls

### Memory Management
- Limit maximum playlist size (500 songs)
- Clean up unused reactive references
- Implement playlist archiving for old playlists

### Storage Efficiency
- Compress playlist data before localStorage
- Implement data migration for schema changes
- Regular cleanup of orphaned data

## Security and Privacy

### Data Protection
- No sensitive data stored in playlists
- Client-side only storage (no server persistence)
- URL sharing doesn't expose personal information

### Input Validation
- Sanitize playlist names
- Validate song IDs format
- Prevent XSS in shared playlist names

## Accessibility

### Keyboard Navigation
- Tab navigation through playlist items
- Keyboard shortcuts for common actions
- Screen reader support for drag and drop

### Visual Design
- High contrast mode support
- Responsive design for mobile devices
- Clear visual feedback for all actions

### Internationalization
- Thai language support for all UI text
- RTL layout considerations
- Localized date/time formatting
