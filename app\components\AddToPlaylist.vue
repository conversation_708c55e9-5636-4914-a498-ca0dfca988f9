<template>
  <div class="add-to-playlist">
    <!-- Add to Playlist Button -->
    <DropdownMenu
      :items="getDropdownItems()"
      :ui="{ content: 'w-64' }"
    >
      <Button
        icon="ph:bookmark-simple-bold"
        size="md"
        :loading="isLoading"
      >
        <span class="hidden md:block">บันทึก</span>
      </Button>

      <!-- No Playlists Slot -->
      <template
        v-if="playlists.length === 0 && !isLoading && isStorageAvailable"
        #content-top
      >
        <div
          v-if="!isStorageAvailable"
          class="border-b bg-yellow-50 p-3 text-sm text-yellow-700"
        >
          <Icon
            name="i-lucide-alert-triangle"
            class="mr-1 inline h-4 w-4"
          />
          localStorage ไม่พร้อมใช้งาน
        </div>
        <div
          v-if="isLoading"
          class="p-3"
        >
          <div class="flex items-center justify-center">
            <Icon
              name="i-lucide-loader-2"
              class="mr-2 h-4 w-4 animate-spin"
            />
            กำลังโหลด...
          </div>
        </div>
        <div
          v-if="playlists.length === 0
            && !isLoading
            && isStorageAvailable"
          class="p-3"
        >
          <p class="mb-3 text-sm text-gray-600">
            ยังไม่มีเพลย์ลิสต์
          </p>
          <Button
            size="sm"
            block
            icon="i-lucide-plus"
            @click="showCreateModal = true"
          >
            สร้างเพลย์ลิสต์ใหม่
          </Button>
        </div>
      </template>
    </DropdownMenu>

    <!-- Create Playlist Modal -->
    <Modal
      v-model:open="showCreateModal"
      title="สร้างเพลย์ลิสต์ใหม่"
    >
      <template #body>
        <form @submit.prevent="createPlaylistAndAdd">
          <div class="mb-4">
            <Label for="new-playlist-name">
              ชื่อเพลย์ลิสต์
            </Label>
            <Input
              id="new-playlist-name"
              v-model="newPlaylistName"
              placeholder="ใส่ชื่อเพลย์ลิสต์..."
              required
              class="mt-1"
            />
          </div>

          <div class="mb-4">
            <Checkbox
              v-model="addSongToNewPlaylist"
              :label="`เพิ่มเพลง ${song.title} ลงในเพลย์ลิสต์นี้`"
            />
          </div>

          <div class="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              @click="showCreateModal = false"
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              color="primary"
              :loading="isCreating"
            >
              สร้าง
            </Button>
          </div>
        </form>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import type { SongItem, SongSmallItem, PlaylistItem } from '~/composables/useSongLoader'

// Props
const props = defineProps<{
  song: SongItem | SongSmallItem
}>()

// Composables
const playlist = usePlaylist()
const dialog = useDialog()
const noti = useNotification()

// Reactive state
const showCreateModal = ref(false)
const newPlaylistName = ref('')
const addSongToNewPlaylist = ref(true)
const isCreating = ref(false)
const addingToPlaylist = ref<string | null>(null)

// Destructure playlist composable
const {
  playlists,
  isLoading,
  isStorageAvailable,
  createPlaylist,
  addSongToPlaylist,
  removeSongFromPlaylist,
  isSongInPlaylist,
  getPlaylistsContainingSong,
} = playlist

// Computed
const hasPlaylistsWithSong = computed(() => {
  return getPlaylistsContainingSong(props.song.id).length > 0
})

// Methods
const getDropdownItems = () => {
  if (!isStorageAvailable.value || isLoading.value) {
    return []
  }

  if (playlists.value.length === 0) {
    return []
  }

  const items = [
    {
      label: 'สร้างเพลย์ลิสต์ใหม่',
      icon: 'i-lucide-plus',
      onSelect: () => {
        showCreateModal.value = true
      },
    },
    {
      type: 'separator' as const,
    },
  ]

  // Add existing playlists
  playlists.value.forEach((playlist) => {
    const isInPlaylist = isSongInPlaylist(playlist.id, props.song.id)
    const playlistItem = playlist as PlaylistItem

    const item: any = {
      label: playlist.name,
      icon: isInPlaylist ? 'i-lucide-check' : 'i-lucide-music',
      onSelect: () => toggleSongInPlaylist(playlistItem),
    }

    if (isInPlaylist) {
      item.color = 'success'
    }

    items.push(item)
  })

  return items
}

const toggleSongInPlaylist = async (playlistItem: PlaylistItem) => {
  if (addingToPlaylist.value) return // Prevent multiple simultaneous operations

  addingToPlaylist.value = playlistItem.id

  try {
    const isInPlaylist = isSongInPlaylist(playlistItem.id, props.song.id)
    let success = false

    if (isInPlaylist) {
      // Remove from playlist
      success = await removeSongFromPlaylist(playlistItem.id, props.song.id)

      if (success) {
        noti.success({
          title: 'ลบออกจากเพลย์ลิสต์แล้ว',
          description: `ลบเพลง "${props.song.title}" ออกจากเพลย์ลิสต์ "${playlistItem.name}" เรียบร้อยแล้ว`,
        })
      }
    } else {
      // Add to playlist
      success = await addSongToPlaylist(playlistItem.id, props.song.id)

      if (success) {
        noti.success({
          title: 'เพิ่มลงเพลย์ลิสต์แล้ว',
          description: `เพิ่มเพลง "${props.song.title}" ลงในเพลย์ลิสต์ "${playlistItem.name}" เรียบร้อยแล้ว`,
        })
      }
    }

    if (!success) {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถดำเนินการได้ กรุณาลองใหม่อีกครั้ง',
      })
    }
  } catch (error) {
    console.error('Error toggling song in playlist:', error)
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'เกิดข้อผิดพลาดในการดำเนินการ กรุณาลองใหม่อีกครั้ง',
    })
  } finally {
    addingToPlaylist.value = null
  }
}

const createPlaylistAndAdd = async () => {
  if (!newPlaylistName.value.trim()) return

  isCreating.value = true

  try {
    const newPlaylist = await createPlaylist(newPlaylistName.value.trim())

    if (newPlaylist) {
      let success = true

      // Add song to new playlist if requested
      if (addSongToNewPlaylist.value) {
        success = await addSongToPlaylist(newPlaylist.id, props.song.id)
      }

      if (success) {
        showCreateModal.value = false
        newPlaylistName.value = ''
        addSongToNewPlaylist.value = true

        const message = addSongToNewPlaylist.value
          ? `สร้างเพลย์ลิสต์ "${newPlaylist.name}" และเพิ่มเพลงเรียบร้อยแล้ว`
          : `สร้างเพลย์ลิสต์ "${newPlaylist.name}" เรียบร้อยแล้ว`

        dialog.success({
          title: 'สำเร็จ',
          description: message,
        })
      } else {
        noti.warning({
          title: 'สร้างเพลย์ลิสต์แล้ว',
          description: `สร้างเพลย์ลิสต์ "${newPlaylist.name}" เรียบร้อยแล้ว แต่ไม่สามารถเพิ่มเพลงได้`,
        })
      }
    } else {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถสร้างเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
      })
    }
  } catch (error) {
    console.error('Error creating playlist:', error)
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'เกิดข้อผิดพลาดในการสร้างเพลย์ลิสต์ กรุณาลองใหม่อีกครั้ง',
    })
  } finally {
    isCreating.value = false
  }
}

// Reset modal state when closed
watch(showCreateModal, (isOpen) => {
  if (!isOpen) {
    newPlaylistName.value = ''
    addSongToNewPlaylist.value = true
  }
})
</script>
