<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="`คอร์ด ${chord}`"
  >
    <template #body>
      <div
        class="flex min-h-20 justify-center"
      >
        <ins
          v-element-visibility="onElementVisibility"
          class="scales_chords_api [&>img:w-full] mx-auto"
          :chord="chord"
          instrument="guitar"
          width="600px"
        />
      </div>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { vElementVisibility } from '@vueuse/components'
import { useMediaQuery } from '@vueuse/core'
import { Modal, Slideover } from '#components'

const emits = defineEmits<{ close: [boolean] }>()

defineProps<{
  chord: string
}>()

const isDesktop = useMediaQuery('(min-width: 768px)')

const onElementVisibility = () => {
  if (window.scales_chords_api_onload) {
    window.scales_chords_api_onload()
  } else {
    setTimeout(onElementVisibility, 1000)
  }
}
</script>
