<template>
  <div class="playlist-song-item bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200">
    <div class="p-4 flex items-center gap-4">
      <!-- Drag Handle -->
      <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600">
        <Icon
          name="lucide:grip-vertical"
          class="h-5 w-5"
        />
      </div>
      
      <!-- Song Number -->
      <div class="text-sm text-gray-500 w-8 text-center">
        {{ index + 1 }}
      </div>
      
      <!-- Song Thumbnail -->
      <div class="flex-shrink-0">
        <Image
          class="w-12 h-12 rounded-md object-cover"
          :src="song.thumbnail"
          :alt="song.title"
        >
          <template #error>
            <div class="flex h-12 w-12 items-center justify-center rounded-md bg-gray-100">
              <Icon
                name="ph:music-notes"
                class="h-6 w-6 text-gray-400"
              />
            </div>
          </template>
        </Image>
      </div>
      
      <!-- Song Info -->
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 truncate">
          {{ song.title }}
        </h3>
        <p class="text-sm text-gray-600 truncate">
          {{ song.artist }}
        </p>
        <div
          v-if="song.key || song.chords.length > 0"
          class="flex items-center gap-2 mt-1"
        >
          <span
            v-if="song.key"
            class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
          >
            Key: {{ song.key }}
          </span>
          <span
            v-if="song.chords.length > 0"
            class="text-xs text-gray-500"
          >
            {{ song.chords.slice(0, 3).join(', ') }}{{ song.chords.length > 3 ? '...' : '' }}
          </span>
        </div>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center gap-2">
        <NuxtLink :to="`/song/${song.id}`">
          <Button
            size="sm"
            variant="outline"
            icon="lucide:external-link"
          >
            เปิด
          </Button>
        </NuxtLink>
        
        <Button
          size="sm"
          variant="ghost"
          icon="lucide:trash-2"
          class="text-red-600 hover:text-red-700"
          @click="$emit('remove')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PlaylistSong } from '~/composables/useSongLoader'

// Props
defineProps<{
  song: PlaylistSong
  index: number
}>()

// Emits
defineEmits<{
  remove: []
}>()
</script>

<style scoped>
.drag-handle:hover {
  cursor: move;
}
</style>
