<template>
  <section class="py-6">
    <h3
      v-if="title"
      class="mb-4 text-2xl font-bold text-white"
    >
      {{ title }}
    </h3>

    <Loader
      :loading="loading"
    >
      <div
        class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
      >
        <SongCard
          v-for="song in songs"
          :key="song.id"
          :song="song"
        />
      </div>
    </Loader>
  </section>
</template>

<script setup lang="ts">
defineProps<{
  title?: string
  loading: boolean
  songs: any[]
}>()
</script>
