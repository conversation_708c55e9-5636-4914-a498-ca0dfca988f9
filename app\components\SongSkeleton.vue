<template>
  <div class="song-skeleton">
    <!-- <PERSON> Header Skeleton -->
    <Card class="mb-6 p-2 lg:p-4">
      <div class="flex flex-col gap-4 md:flex-row">
        <!-- Thumbnail Skeleton -->
        <Skeleton class="h-[150px] w-full rounded-md md:h-[80px] md:w-[80px]" />

        <div class="flex-1">
          <!-- Title Skeleton -->
          <Skeleton class="mb-2 h-8 w-3/4 rounded" />
          <!-- Artist Skeleton -->
          <Skeleton class="mb-3 h-6 w-1/2 rounded" />
          <!-- Genre Badge Skeleton -->
          <Skeleton class="h-6 w-20 rounded-full" />
        </div>
      </div>

      <!-- Chords Section Skeleton -->
      <div class="mt-4">
        <div class="flex flex-wrap gap-2">
          <Skeleton
            v-for="i in 4"
            :key="i"
            class="h-8 w-12 rounded-full"
          />
        </div>
      </div>
    </Card>

    <!-- Controls Skeleton -->
    <div class="mb-6 flex flex-col items-center justify-center gap-2 lg:flex-row lg:flex-wrap">
      <div class="flex items-center gap-2">
        <!-- Control Buttons Skeleton -->
        <Skeleton class="h-8 w-24 rounded" />
        <Skeleton class="h-8 w-32 rounded" />
      </div>

      <div class="flex flex-col items-center justify-center gap-2 lg:flex-row lg:flex-wrap">
        <div class="flex items-center gap-2">
          <!-- Key and Tuning Skeleton -->
          <Skeleton class="h-6 w-16 rounded-full" />
          <Skeleton class="h-6 w-20 rounded-full" />
        </div>

        <div class="flex items-center gap-2">
          <!-- Transpose Controls Skeleton -->
          <Skeleton class="h-8 w-8 rounded-full" />
          <Skeleton class="h-4 w-16 rounded" />
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col gap-4 lg:flex-row">
      <!-- Lyrics Content Skeleton -->
      <div class="flex-1">
        <div class="space-y-6 rounded-lg bg-white/80 py-6">
          <!-- Multiple sections of lyrics skeleton -->
          <div
            v-for="section in 4"
            :key="section"
            class="space-y-3 px-2 md:px-6"
          >
            <!-- Section lines -->
            <div
              v-for="line in 6"
              :key="line"
              class="flex flex-col gap-1"
            >
              <!-- Chord line skeleton -->
              <div class="flex gap-4">
                <Skeleton
                  v-for="chord in Math.floor(Math.random() * 4) + 2"
                  :key="chord"
                  class="h-5 w-8 rounded"
                />
              </div>
              <!-- Lyrics line skeleton -->
              <Skeleton
                class="h-6 rounded"
                :class="[
                  line % 3 === 0 ? 'w-3/4' : line % 2 === 0 ? 'w-full' : 'w-5/6'
                ]"
              />
            </div>

            <!-- Section separator -->
            <div class="py-2">
              <Skeleton class="mx-auto h-1 w-16 rounded" />
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar Skeleton -->
      <div class="lg:w-64">
        <!-- Artist Section Skeleton -->
        <Card class="mb-4 text-center">
          <Skeleton class="mx-auto h-6 w-32 rounded" />
        </Card>

        <!-- Related Songs Skeleton -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-1">
          <div
            v-for="i in 6"
            :key="i"
            class="rounded-lg bg-white/70 p-3"
          >
            <div class="flex gap-3">
              <Skeleton class="h-12 w-12 rounded" />
              <div class="flex-1">
                <Skeleton class="mb-1 h-4 w-full rounded" />
                <Skeleton class="h-3 w-2/3 rounded" />
              </div>
            </div>
          </div>
        </div>

        <!-- View All Button Skeleton -->
        <div class="mt-4 flex justify-center">
          <Skeleton class="h-10 w-full rounded" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props needed for skeleton loader
</script>
