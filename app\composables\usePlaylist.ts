import type { PlaylistItem, <PERSON>list<PERSON>ong, <PERSON><PERSON><PERSON>, SongSmallItem } from './useSongLoader'

/**
 * Main playlist management composable
 * Provides reactive state management and operations for playlists
 */
export const usePlaylist = () => {
  const storage = usePlaylistStorage()

  // Reactive state
  const playlists = ref<PlaylistItem[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Load playlists from storage
  const loadPlaylists = () => {
    try {
      isLoading.value = true
      error.value = null

      const data = storage.loadPlaylists()

      playlists.value = data.playlists
    } catch (err) {
      error.value = 'Failed to load playlists'
      console.error('Error loading playlists:', err)
    } finally {
      isLoading.value = false
    }
  }

  // Create new playlist
  const createPlaylist = async (name: string): Promise<PlaylistItem | null> => {
    try {
      error.value = null

      const newPlaylist = storage.createPlaylist(name)

      if (newPlaylist) {
        playlists.value.push(newPlaylist)

        return newPlaylist
      }

      error.value = 'Failed to create playlist'

      return null
    } catch (err) {
      error.value = 'Failed to create playlist'
      console.error('Error creating playlist:', err)

      return null
    }
  }

  // Update playlist
  const updatePlaylist = async (id: string, updates: Partial<Omit<PlaylistItem, 'id' | 'createdAt'>>): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.updatePlaylist(id, updates)

      if (success) {
        const index = playlists.value.findIndex((p) => p.id === id)

        if (index !== -1) {
          playlists.value[index] = {
            ...playlists.value[index],
            ...updates,
            updatedAt: new Date().toISOString(),
          }
        }

        return true
      }

      error.value = 'Failed to update playlist'

      return false
    } catch (err) {
      error.value = 'Failed to update playlist'
      console.error('Error updating playlist:', err)

      return false
    }
  }

  // Delete playlist
  const deletePlaylist = async (id: string): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.deletePlaylist(id)

      if (success) {
        playlists.value = playlists.value.filter((p) => p.id !== id)

        return true
      }

      error.value = 'Failed to delete playlist'

      return false
    } catch (err) {
      error.value = 'Failed to delete playlist'
      console.error('Error deleting playlist:', err)

      return false
    }
  }

  // Add song to playlist
  const addSongToPlaylist = async (playlistId: string, songId: string): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.addSongToPlaylist(playlistId, songId)

      if (success) {
        const playlist = playlists.value.find((p) => p.id === playlistId)

        if (playlist) {
          playlist.songIds.push(songId)
          playlist.updatedAt = new Date().toISOString()
        }

        return true
      }

      error.value = 'Failed to add song to playlist'

      return false
    } catch (err) {
      error.value = 'Failed to add song to playlist'
      console.error('Error adding song to playlist:', err)

      return false
    }
  }

  // Remove song from playlist
  const removeSongFromPlaylist = async (playlistId: string, songId: string): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.removeSongFromPlaylist(playlistId, songId)

      if (success) {
        const playlist = playlists.value.find((p) => p.id === playlistId)

        if (playlist) {
          playlist.songIds = playlist.songIds.filter((id) => id !== songId)
          playlist.updatedAt = new Date().toISOString()
        }

        return true
      }

      error.value = 'Failed to remove song from playlist'

      return false
    } catch (err) {
      error.value = 'Failed to remove song from playlist'
      console.error('Error removing song from playlist:', err)

      return false
    }
  }

  // Reorder songs in playlist
  const reorderPlaylistSongs = async (playlistId: string, newOrder: string[]): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.reorderPlaylistSongs(playlistId, newOrder)

      if (success) {
        const playlist = playlists.value.find((p) => p.id === playlistId)

        if (playlist) {
          playlist.songIds = newOrder
          playlist.updatedAt = new Date().toISOString()
        }

        return true
      }

      error.value = 'Failed to reorder songs'

      return false
    } catch (err) {
      error.value = 'Failed to reorder songs'
      console.error('Error reordering songs:', err)

      return false
    }
  }

  // Get playlist by ID
  const getPlaylistById = (id: string): PlaylistItem | undefined => {
    return playlists.value.find((p) => p.id === id)
  }

  // Get playlist by name
  const getPlaylistByName = (name: string): PlaylistItem | undefined => {
    return playlists.value.find((p) => p.name.toLowerCase() === name.toLowerCase())
  }

  // Check if song is in playlist
  const isSongInPlaylist = (playlistId: string, songId: string): boolean => {
    const playlist = getPlaylistById(playlistId)

    return playlist ? playlist.songIds.includes(songId) : false
  }

  // Get playlists containing a specific song
  const getPlaylistsContainingSong = (songId: string): PlaylistItem[] => {
    return playlists.value.filter((playlist) => playlist.songIds.includes(songId))
  }

  // Convert SongItem to PlaylistSong
  const songToPlaylistSong = (song: SongItem | SongSmallItem): PlaylistSong => {
    return {
      id: song.id,
      title: song.title,
      artist: song.artist,
      artist_slug: song.artist_slug,
      thumbnail: song.thumbnail,
      key: 'key' in song ? song.key : '',
      chords: 'chords' in song ? song.chords : [],
    }
  }

  // Generate share URL for playlist
  const generateShareUrl = (playlist: PlaylistItem): string => {
    const baseUrl = window.location.origin
    const encodedName = encodeURIComponent(playlist.name)
    const songIds = playlist.songIds.join(',')

    return `${baseUrl}/playlists/${encodedName}?ids=${songIds}`
  }

  // Parse shared playlist from URL
  const parseSharedPlaylist = (name: string, idsParam: string): { name: string
    songIds: string[] } | null => {
    try {
      const decodedName = decodeURIComponent(name)
      const songIds = idsParam.split(',').filter((id) => id.trim())

      if (!decodedName || songIds.length === 0) {
        return null
      }

      return {
        name: decodedName,
        songIds,
      }
    } catch (err) {
      console.error('Error parsing shared playlist:', err)

      return null
    }
  }

  // Export playlists
  const exportPlaylists = (): string => {
    return storage.exportPlaylists()
  }

  // Import playlists
  const importPlaylists = async (jsonData: string, merge: boolean = false): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.importPlaylists(jsonData, merge)

      if (success) {
        loadPlaylists() // Reload to reflect changes

        return true
      }

      error.value = 'Failed to import playlists'

      return false
    } catch (err) {
      error.value = 'Failed to import playlists'
      console.error('Error importing playlists:', err)

      return false
    }
  }

  // Clear all playlists
  const clearAllPlaylists = async (): Promise<boolean> => {
    try {
      error.value = null

      const success = storage.clearAllPlaylists()

      if (success) {
        playlists.value = []

        return true
      }

      error.value = 'Failed to clear playlists'

      return false
    } catch (err) {
      error.value = 'Failed to clear playlists'
      console.error('Error clearing playlists:', err)

      return false
    }
  }

  // Computed properties
  const playlistCount = computed(() => playlists.value.length)
  const totalSongs = computed(() => playlists.value.reduce((total, playlist) => total + playlist.songIds.length, 0))
  const isStorageAvailable = computed(() => storage.isStorageAvailable())

  // Initialize on first use
  onMounted(() => {
    loadPlaylists()
  })

  return {
    // State
    playlists: readonly(playlists),
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Actions
    loadPlaylists,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    addSongToPlaylist,
    removeSongFromPlaylist,
    reorderPlaylistSongs,

    // Getters
    getPlaylistById,
    getPlaylistByName,
    isSongInPlaylist,
    getPlaylistsContainingSong,

    // Utilities
    songToPlaylistSong,
    generateShareUrl,
    parseSharedPlaylist,
    exportPlaylists,
    importPlaylists,
    clearAllPlaylists,

    // Computed
    playlistCount,
    totalSongs,
    isStorageAvailable,
  }
}
