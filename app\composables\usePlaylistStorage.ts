import type { PlaylistItem, StoredPlaylistData } from './useSongLoader'

const STORAGE_KEY = 'mhalong_playlists'
const STORAGE_VERSION = '1.0.0'

/**
 * Composable for managing playlist data in localStorage
 * Provides CRUD operations with error handling and data validation
 */
export const usePlaylistStorage = () => {
  // Check if localStorage is available
  const isStorageAvailable = (): boolean => {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        return false
      }

      // Test localStorage functionality
      const testKey = '__mhalong_test__'

      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)

      return true
    } catch {
      return false
    }
  }

  // Get default storage data structure
  const getDefaultData = (): StoredPlaylistData => ({
    version: STORAGE_VERSION,
    playlists: [],
    lastUpdated: new Date().toISOString(),
  })

  // Load playlists from localStorage
  const loadPlaylists = (): StoredPlaylistData => {
    if (!isStorageAvailable()) {
      console.warn('localStorage is not available, using default data')

      return getDefaultData()
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY)

      if (!stored) {
        return getDefaultData()
      }

      const parsed = JSON.parse(stored) as StoredPlaylistData

      // Validate data structure
      if (!parsed.version || !Array.isArray(parsed.playlists)) {
        console.warn('Invalid playlist data structure, resetting to default')

        return getDefaultData()
      }

      // Validate each playlist
      const validPlaylists = parsed.playlists.filter((playlist): playlist is PlaylistItem => {
        return (
          typeof playlist.id === 'string'
          && typeof playlist.name === 'string'
          && Array.isArray(playlist.songIds)
          && typeof playlist.createdAt === 'string'
          && typeof playlist.updatedAt === 'string'
        )
      })

      return {
        ...parsed,
        playlists: validPlaylists,
        lastUpdated: parsed.lastUpdated || new Date().toISOString(),
      }
    } catch (error) {
      console.error('Error loading playlists from localStorage:', error)

      return getDefaultData()
    }
  }

  // Save playlists to localStorage
  const savePlaylists = (data: StoredPlaylistData): boolean => {
    if (!isStorageAvailable()) {
      console.warn('localStorage is not available, cannot save playlists')

      return false
    }

    try {
      const dataToSave = {
        ...data,
        lastUpdated: new Date().toISOString(),
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave))

      return true
    } catch (error) {
      console.error('Error saving playlists to localStorage:', error)

      return false
    }
  }

  // Create a new playlist
  const createPlaylist = (name: string): PlaylistItem | null => {
    if (!name.trim()) {
      console.error('Playlist name cannot be empty')

      return null
    }

    const data = loadPlaylists()

    // Check if playlist name already exists
    if (data.playlists.some((p) => p.name.toLowerCase() === name.toLowerCase())) {
      console.error('Playlist with this name already exists')

      return null
    }

    const newPlaylist: PlaylistItem = {
      id: generatePlaylistId(),
      name: name.trim(),
      songIds: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    data.playlists.push(newPlaylist)

    if (savePlaylists(data)) {
      return newPlaylist
    }

    return null
  }

  // Update an existing playlist
  const updatePlaylist = (id: string, updates: Partial<Omit<PlaylistItem, 'id' | 'createdAt'>>): boolean => {
    const data = loadPlaylists()
    const playlistIndex = data.playlists.findIndex((p) => p.id === id)

    if (playlistIndex === -1) {
      console.error('Playlist not found')

      return false
    }

    // If updating name, check for duplicates
    if (updates.name) {
      const nameExists = data.playlists.some((p, index) =>
        index !== playlistIndex && p.name.toLowerCase() === updates.name!.toLowerCase(),
      )

      if (nameExists) {
        console.error('Playlist with this name already exists')

        return false
      }
    }

    data.playlists[playlistIndex] = {
      ...data.playlists[playlistIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    }

    return savePlaylists(data)
  }

  // Delete a playlist
  const deletePlaylist = (id: string): boolean => {
    const data = loadPlaylists()
    const initialLength = data.playlists.length

    data.playlists = data.playlists.filter((p) => p.id !== id)

    if (data.playlists.length === initialLength) {
      console.error('Playlist not found')

      return false
    }

    return savePlaylists(data)
  }

  // Add song to playlist
  const addSongToPlaylist = (playlistId: string, songId: string): boolean => {
    const data = loadPlaylists()
    const playlist = data.playlists.find((p) => p.id === playlistId)

    if (!playlist) {
      console.error('Playlist not found')

      return false
    }

    if (playlist.songIds.includes(songId)) {
      console.warn('Song already exists in playlist')

      return false
    }

    playlist.songIds.push(songId)
    playlist.updatedAt = new Date().toISOString()

    return savePlaylists(data)
  }

  // Remove song from playlist
  const removeSongFromPlaylist = (playlistId: string, songId: string): boolean => {
    const data = loadPlaylists()
    const playlist = data.playlists.find((p) => p.id === playlistId)

    if (!playlist) {
      console.error('Playlist not found')

      return false
    }

    const initialLength = playlist.songIds.length

    playlist.songIds = playlist.songIds.filter((id) => id !== songId)

    if (playlist.songIds.length === initialLength) {
      console.warn('Song not found in playlist')

      return false
    }

    playlist.updatedAt = new Date().toISOString()

    return savePlaylists(data)
  }

  // Reorder songs in playlist
  const reorderPlaylistSongs = (playlistId: string, newOrder: string[]): boolean => {
    const data = loadPlaylists()
    const playlist = data.playlists.find((p) => p.id === playlistId)

    if (!playlist) {
      console.error('Playlist not found')

      return false
    }

    // Validate that newOrder contains the same songs
    if (newOrder.length !== playlist.songIds.length
      || !newOrder.every((id) => playlist.songIds.includes(id))) {
      console.error('Invalid song order provided')

      return false
    }

    playlist.songIds = newOrder
    playlist.updatedAt = new Date().toISOString()

    return savePlaylists(data)
  }

  // Export playlists as JSON
  const exportPlaylists = (): string => {
    const data = loadPlaylists()

    return JSON.stringify(data, null, 2)
  }

  // Import playlists from JSON
  const importPlaylists = (jsonData: string, merge: boolean = false): boolean => {
    try {
      const importedData = JSON.parse(jsonData) as StoredPlaylistData

      // Validate imported data
      if (!importedData.version || !Array.isArray(importedData.playlists)) {
        console.error('Invalid import data structure')

        return false
      }

      if (merge) {
        const currentData = loadPlaylists()
        const mergedPlaylists = [...currentData.playlists]

        // Add imported playlists, handling name conflicts
        importedData.playlists.forEach((importedPlaylist) => {
          let playlistName = importedPlaylist.name
          let counter = 1

          // Handle name conflicts by appending numbers
          while (mergedPlaylists.some((p) => p.name.toLowerCase() === playlistName.toLowerCase())) {
            playlistName = `${importedPlaylist.name} (${counter})`
            counter++
          }

          mergedPlaylists.push({
            ...importedPlaylist,
            id: generatePlaylistId(),
            name: playlistName,
            updatedAt: new Date().toISOString(),
          })
        })

        const mergedData: StoredPlaylistData = {
          version: STORAGE_VERSION,
          playlists: mergedPlaylists,
          lastUpdated: new Date().toISOString(),
        }

        return savePlaylists(mergedData)
      }

      // Replace all playlists
      const newData: StoredPlaylistData = {
        version: STORAGE_VERSION,
        playlists: importedData.playlists.map((playlist) => ({
          ...playlist,
          id: generatePlaylistId(),
          updatedAt: new Date().toISOString(),
        })),
        lastUpdated: new Date().toISOString(),
      }

      return savePlaylists(newData)
    } catch (error) {
      console.error('Error importing playlists:', error)

      return false
    }
  }

  // Clear all playlists
  const clearAllPlaylists = (): boolean => {
    return savePlaylists(getDefaultData())
  }

  // Generate unique playlist ID
  const generatePlaylistId = (): string => {
    return `playlist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  return {
    isStorageAvailable,
    loadPlaylists,
    savePlaylists,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    addSongToPlaylist,
    removeSongFromPlaylist,
    reorderPlaylistSongs,
    exportPlaylists,
    importPlaylists,
    clearAllPlaylists,
  }
}
