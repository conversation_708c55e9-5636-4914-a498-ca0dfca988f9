import type { AxiosRequestConfig } from 'axios'

export const useRequestOptions = () => {
  const config = useRuntimeConfig()

  const mock = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseAPIMock || 'http://localhost:3000/api/mock',
    }
  }

  const base = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    return {
      baseURL: config.public.baseInternalAPI,
    }
  }

  const auth = (): Omit<AxiosRequestConfig, 'baseURL'> & {
    baseURL: string
  } => {
    const token = useCookie('authorization')

    return {
      baseURL: config.public.baseInternalAPI,
      headers: {
        Authorization: `Bearer ${token.value}`,
      },
    }
  }

  return {
    base,
    mock,
    auth,
  }
}
