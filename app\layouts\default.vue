<template>
  <div class="min-h-screen">
    <!-- Header -->
    <header class="text-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex min-h-18 flex-row items-center justify-between gap-4 py-4">
          <div class="flex items-center gap-4">
            <NuxtLink
              class="flex items-center gap-4"
              to="/"
            >
              <img
                src="/logo.png"
                class="w-[36px] min-w-[36px]"
                alt=""
              />
              <p class="hidden text-2xl font-bold md:block">
                Mhalong chords
              </p>
            </NuxtLink>

            <!-- Navigation Links -->
            <nav class="ml-6 hidden items-center gap-4 md:flex">
              <NuxtLink
                to="/songs"
                class="font-medium text-white/80 transition-colors duration-200 hover:text-white"
                :class="{ 'font-bold text-white': $route.path === '/songs' }"
              >
                เพลง
              </NuxtLink>
              <NuxtLink
                to="/playlists"
                class="font-medium text-white/80 transition-colors duration-200 hover:text-white"
                :class="{ 'font-bold text-white': $route.path.startsWith('/playlists') }"
              >
                เพลย์ลิสต์
              </NuxtLink>
            </nav>
          </div>

          <div class="flex flex-1 items-center justify-end gap-4">
            <Input
              v-if="$route.path !== '/' && $route.path !== '/songs'"
              v-model="searchQuery"
              size="lg"
              type="text"
              leading-icon="lucide:search"
              inputmode="search"
              placeholder="ค้นหาเพลง หรือ คอร์ด..."
              class="w-full md:w-auto"
              :ui="{
                base: 'bg-white/70',
              }"
              @focus="selectAllText"
              @keyup.enter="searchSongs"
            />

            <!-- Mobile Navigation Menu -->
            <div class="md:hidden">
              <Button
                size="sm"
                square
                variant="ghost"
                class="text-white"
                @click="showMobileMenu = !showMobileMenu"
              >
                <Icon
                  name="lucide:menu"
                  class="h-5 w-5"
                />
              </Button>
            </div>
          </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div
          v-if="showMobileMenu"
          class="border-t border-white/20 bg-white/10 backdrop-blur-sm md:hidden"
        >
          <div class="space-y-2 px-4 py-3">
            <NuxtLink
              to="/songs"
              class="block py-2 font-medium text-white/80 transition-colors duration-200 hover:text-white"
              :class="{ 'font-bold text-white': $route.path === '/songs' }"
              @click="showMobileMenu = false"
            >
              เพลง
            </NuxtLink>
            <NuxtLink
              to="/playlists"
              class="block py-2 font-medium text-white/80 transition-colors duration-200 hover:text-white"
              :class="{ 'font-bold text-white': $route.path.startsWith('/playlists') }"
              @click="showMobileMenu = false"
            >
              เพลย์ลิสต์
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>
    <div class="mx-auto max-w-7xl px-2 pt-8 pb-[120px] sm:px-6 lg:px-8">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup>
const searchQuery = ref('')
const showMobileMenu = ref(false)

const searchSongs = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/songs?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

const selectAllText = (event: Event) => {
  const target = event.target as HTMLInputElement

  target.select()
}

// Close mobile menu when route changes
const route = useRoute()

watch(() => route.path, () => {
  showMobileMenu.value = false
})
</script>
