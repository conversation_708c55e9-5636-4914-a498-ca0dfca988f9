<template>
  <!-- Search and Filters -->
  <div class="mb-8 rounded-lg bg-white/80 p-6 shadow-sm">
    <p class="flex-1 text-xl font-bold">
      Artist : {{ search.fetch.items?.[0]?.artist || route.params.id }}
    </p>
  </div>

  <FlexDeck
    :options="flexOptions"
    container-class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
    @pageChange="search.fetchPageChange"
    @search="search.fetchSearch"
  >
    <template #loading-state>
      <SongListSkeleton />
    </template>
    <template #default="{ row }: { row: SongSmallItem }">
      <SongCard
        :song="row"
      />
    </template>
  </FlexDeck>
</template>

<script setup lang="ts">
const currentPage = ref(1)
const route = useRoute()
const search = useSongByArtistLoader()

// Methods
const fetchSongs = async () => {
  search.fetchPage(currentPage.value, route.params.id as string)
}

search.fetchSetLoading()
onMounted(async () => {
  fetchSongs()
})

const flexOptions = useFlexDeck<SongSmallItem>({
  repo: search,
})
</script>
