<template>
  <!-- Hero Section -->
  <section class="py-20">
    <div class="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
      <h2 class="mb-6 text-4xl font-bold text-white md:text-6xl">
        เพลงและคอร์ดกีตาร์
      </h2>
      <p class="mx-auto mb-8 max-w-3xl text-xl text-white">
        ค้นหาเพลงพร้อมเนื้อเพลงและคอร์ดกีตาร์ที่ละเอียด
        เหมาะสำหรับการฝึกซ้อมและเรียนรู้เพลงใหม่ๆ
      </p>

      <!-- Search Bar -->
      <div class="mx-auto mb-12 max-w-md">
        <Input
          v-model="searchQuery"
          inputmode="search"
          leading-icon="lucide:search"
          placeholder="ค้นหาเพลง ศิลปิน หรือคอร์ด..."
          @keyup.enter="searchSongs"
        />
        <Button
          block
          color="secondary"
          class="mt-4"
          @click="searchSongs"
        >
          ค้นหาเพลง
        </Button>
      </div>
    </div>
  </section>

  <template v-if="section.status.value.isLoading">
    <SongListSkeleton
      v-for="i in [1, 2, 3]"
      :key="i"
    />
  </template>
  <SongList
    v-for="sectionItem in section.items.value"
    :key="sectionItem.title"
    :title="sectionItem.title.replace('คอร์ดเพลง', '')"
    :loading="false"
    :songs="sectionItem.songs"
  />
</template>

<script setup lang="ts">
const searchQuery = ref('')
const section = useSongSectionsLoader()

section.setLoading()
onMounted(async () => {
  section.run()
})

const searchSongs = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/songs?q=${encodeURIComponent(searchQuery.value)}`)
  }
}
</script>
