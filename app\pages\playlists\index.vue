<template>
  <div class="playlists-page">
    <!-- <PERSON> Header -->
    <div class="mb-8 flex flex-col items-center justify-between gap-4 lg:flex-row lg:items-start">
      <div>
        <h1 class="mb-2 text-3xl font-bold">
          เพลย์ลิสต์ของฉัน
        </h1>
        <p class="text-gray-600">
          จัดการและเล่นเพลงโปรดของคุณ
        </p>
      </div>
      <div class="flex items-center justify-end gap-4">
        <Button
          size="md"
          color="primary"
          icon="lucide:plus"
          class="ring-2 ring-white"
          @click="showCreateModal = true"
        >
          <span class="hidden md:block">สร้าง</span>
        </Button>

        <Button
          size="md"
          color="primary"
          class="ring-2 ring-white"
          icon="lucide:download"
          @click="exportPlaylists"
        >
          ส่งออก
        </Button>

        <Button
          size="md"
          color="primary"
          class="ring-2 ring-white"
          icon="lucide:upload"
          @click="showImportModal = true"
        >
          นำเข้า
        </Button>
      </div>
    </div>

    <!-- Search Bar -->
    <div
      v-if="playlistCount > 0"
      class="mb-6"
    >
      <Input
        v-model="searchQuery"
        size="lg"
        placeholder="ค้นหาเพลย์ลิสต์..."
        leading-icon="lucide:search"
        class="max-w-md"
      />
    </div>

    <!-- Loading State -->
    <div
      v-if="isLoading || !isStorageAvailable"
      class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
    >
      <div
        v-for="i in 6"
        :key="i"
        class="rounded-lg bg-white/40 p-6 shadow-sm"
      >
        <div class="animate-pulse">
          <div class="mb-3 h-6 rounded bg-gray-200" />
          <div class="mb-2 h-4 w-2/3 rounded bg-gray-200" />
          <div class="h-4 w-1/2 rounded bg-gray-200" />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <Card
      v-else-if="playlistCount === 0"
      class="py-12 text-center"
    >
      <Icon
        name="lucide:music"
        class="mx-auto mb-4 h-16 w-16 text-gray-400"
      />
      <h3 class="mb-2 text-lg font-medium text-gray-900">
        ยังไม่มีเพลย์ลิสต์
      </h3>
      <p class="mb-6 text-gray-600">
        เริ่มต้นสร้างเพลย์ลิสต์แรกของคุณเพื่อจัดเก็บเพลงโปรด
      </p>
      <Button
        color="primary"
        icon="lucide:plus"
        @click="showCreateModal = true"
      >
        สร้างเพลย์ลิสต์ใหม่
      </Button>
    </Card>

    <!-- Playlists Grid -->
    <div
      v-else
    >
      <div v-if="filteredPlaylists.length === 0">
        <Card
          class="py-12 text-center"
        >
          <Icon
            name="lucide:music"
            class="mx-auto mb-4 h-16 w-16 text-gray-400"
          />
          <h3 class="mb-2 text-lg font-medium text-gray-900">
            ไม่พบเพลย์ลิสต์
          </h3>
          <p class="mb-6 text-gray-600">
            ไม่พบเพลย์ลิสต์ที่ตรงกับคำค้นหา
          </p>
        </Card>
      </div>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="playlist in filteredPlaylists"
          :key="playlist.id"
          class="rounded-lg bg-white shadow-sm transition-shadow duration-200 hover:shadow-md"
        >
          <div class="p-6">
            <div class="mb-3 flex items-start justify-between">
              <div>
                <h3 class="truncate text-lg font-semibold text-gray-900">
                  {{ playlist.name }}
                </h3>
                <span class="text-sm text-gray-600">
                  แก้ไขล่าสุด {{ TimeHelper.displayDateTime(playlist.updatedAt || playlist.createdAt) }}
                </span>
              </div>

              <DropdownMenu :items="getPlaylistMenuItems(playlist as any)">
                <Button
                  size="sm"
                  square
                  variant="ghost"
                  icon="i-lucide-more-vertical"
                />
              </DropdownMenu>
            </div>

            <div class="mb-4 text-sm text-gray-600">
              <p>{{ playlist.songIds.length }} เพลง</p>
            </div>

            <NuxtLink
              :to="`/playlists/${encodeURIComponent(playlist.name)}`"
              class="block"
            >
              <Button
                block
                variant="outline"
                icon="lucide:play"
              >
                เปิดเพลย์ลิสต์
              </Button>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Playlist Modal -->
    <Modal
      v-model:open="showCreateModal"
      title="สร้างเพลย์ลิสต์ใหม่"
    >
      <template #body>
        <form @submit.prevent="createNewPlaylist">
          <div class="mb-4">
            <Label for="playlist-name">
              ชื่อเพลย์ลิสต์
            </Label>
            <Input
              id="playlist-name"
              v-model="newPlaylistName"
              placeholder="ใส่ชื่อเพลย์ลิสต์..."
              required
              class="mt-1"
            />
          </div>

          <div class="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              @click="showCreateModal = false"
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              color="primary"
              :loading="isCreating"
            >
              สร้าง
            </Button>
          </div>
        </form>
      </template>
    </Modal>

    <!-- Edit Playlist Modal -->
    <Modal
      v-model:open="showEditModal"
      title="แก้ไขชื่อเพลย์ลิสต์"
    >
      <template #body>
        <form @submit.prevent="updatePlaylistName">
          <div class="mb-4">
            <Label for="edit-playlist-name">
              ชื่อเพลย์ลิสต์
            </Label>
            <Input
              id="edit-playlist-name"
              v-model="editPlaylistName"
              placeholder="ใส่ชื่อเพลย์ลิสต์..."
              required
              class="mt-1"
            />
          </div>

          <div class="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              @click="showEditModal = false"
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              color="primary"
              :loading="isUpdating"
            >
              บันทึก
            </Button>
          </div>
        </form>
      </template>
    </Modal>

    <!-- Import Modal -->
    <Modal
      v-model:open="showImportModal"
      title="นำเข้าเพลย์ลิสต์"
    >
      <template #body>
        <form @submit.prevent="importPlaylistData">
          <div class="mb-4">
            <Label for="import-data">
              ข้อมูล JSON
            </Label>
            <Textarea
              id="import-data"
              v-model="importData"
              placeholder="วางข้อมูล JSON ที่ส่งออกมาที่นี่..."
              :rows="6"
              required
              class="mt-1"
            />
          </div>

          <div class="mb-4">
            <Checkbox
              v-model="mergeImport"
              label="รวมกับเพลย์ลิสต์ที่มีอยู่ (ไม่เลือกจะแทนที่ทั้งหมด)"
            />
          </div>

          <div class="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              @click="showImportModal = false"
            >
              ยกเลิก
            </Button>
            <Button
              type="submit"
              color="primary"
              :loading="isImporting"
            >
              นำเข้า
            </Button>
          </div>
        </form>
      </template>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import type { PlaylistItem } from '~/composables/useSongLoader'

// Page meta
useHead({
  title: 'เพลย์ลิสต์ของฉัน | Mhalong chords',
  meta: [
    {
      name: 'description',
      content: 'จัดการเพลย์ลิสต์เพลงโปรดของคุณ สร้าง แก้ไข และแชร์เพลย์ลิสต์ได้อย่างง่ายดาย',
    },
  ],
})

// Composables
const playlist = usePlaylist()
const dialog = useDialog()
const noti = useNotification()

// Reactive state
const searchQuery = ref('')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showImportModal = ref(false)
const newPlaylistName = ref('')
const editPlaylistName = ref('')
const editingPlaylist = ref<PlaylistItem | null>(null)
const importData = ref('')
const mergeImport = ref(true)
const isCreating = ref(false)
const isUpdating = ref(false)
const isImporting = ref(false)

// Computed
const filteredPlaylists = computed<PlaylistItem[]>(() => {
  if (!searchQuery.value.trim()) {
    return playlist.playlists.value as PlaylistItem[]
  }

  const query = searchQuery.value.toLowerCase()

  return playlist.playlists.value.filter((p) =>
    p.name.toLowerCase().includes(query),
  ) as PlaylistItem[]
})

// Destructure playlist composable
const {
  playlists,
  isLoading,
  error,
  playlistCount,
  isStorageAvailable,
  createPlaylist,
  updatePlaylist,
  deletePlaylist,
  exportPlaylists: exportPlaylistsData,
  importPlaylists,
} = playlist

// Methods
const getPlaylistMenuItems = (playlistItem: PlaylistItem) => [
  {
    label: 'แก้ไขชื่อ',
    icon: 'i-lucide-edit',
    onSelect: () => editPlaylist(playlistItem),
  },
  {
    label: 'แชร์',
    icon: 'i-lucide-share',
    onSelect: () => sharePlaylist(playlistItem),
  },
  {
    type: 'separator' as const,
  },
  {
    label: 'ลบ',
    icon: 'i-lucide-trash-2',
    color: 'error' as const,
    onSelect: () => confirmDeletePlaylist(playlistItem),
  },
]

const createNewPlaylist = async () => {
  if (!newPlaylistName.value.trim()) return

  isCreating.value = true

  try {
    const created = await createPlaylist(newPlaylistName.value.trim())

    if (created) {
      showCreateModal.value = false
      newPlaylistName.value = ''

      noti.success({
        title: 'สำเร็จ',
        description: 'สร้างเพลย์ลิสต์เรียบร้อยแล้ว',
      })
    } else {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถสร้างเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
      })
    }
  } finally {
    isCreating.value = false
  }
}

const editPlaylist = (playlistItem: PlaylistItem) => {
  editingPlaylist.value = playlistItem
  editPlaylistName.value = playlistItem.name
  showEditModal.value = true
}

const updatePlaylistName = async () => {
  if (!editingPlaylist.value || !editPlaylistName.value.trim()) return

  isUpdating.value = true

  try {
    const success = await updatePlaylist(editingPlaylist.value.id, {
      name: editPlaylistName.value.trim(),
    })

    if (success) {
      showEditModal.value = false
      editingPlaylist.value = null
      editPlaylistName.value = ''

      noti.success({
        title: 'สำเร็จ',
        description: 'แก้ไขชื่อเพลย์ลิสต์เรียบร้อยแล้ว',
      })
    } else {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถแก้ไขชื่อเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
      })
    }
  } finally {
    isUpdating.value = false
  }
}

const confirmDeletePlaylist = async (playlistItem: PlaylistItem) => {
  const confirmed = await dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบเพลย์ลิสต์ "${playlistItem.name}" หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`,
    confirmText: 'ลบ',
    cancelText: 'ยกเลิก',
  })

  if (confirmed) {
    await deletePlaylistConfirmed(playlistItem.id)
  }
}

const deletePlaylistConfirmed = async (id: string) => {
  const success = await deletePlaylist(id)

  if (success) {
    noti.success({
      title: 'สำเร็จ',
      description: 'ลบเพลย์ลิสต์เรียบร้อยแล้ว',
    })
  } else {
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถลบเพลย์ลิสต์ได้ กรุณาลองใหม่อีกครั้ง',
    })
  }
}

const sharePlaylist = async (playlistItem: PlaylistItem) => {
  if (playlistItem.songIds.length === 0) {
    noti.warning({
      title: 'ไม่สามารถแชร์ได้',
      description: 'เพลย์ลิสต์นี้ยังไม่มีเพลง กรุณาเพิ่มเพลงก่อนแชร์',
    })

    return
  }

  const shareUrl = playlist.generateShareUrl(playlistItem)

  try {
    await navigator.clipboard.writeText(shareUrl)
    noti.success({
      title: 'คัดลอกลิงก์แล้ว',
      description: 'ลิงก์แชร์เพลย์ลิสต์ถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
    })

    await navigator.share({
      title: `เพลย์ลิสต์: ${playlistItem.name}`,
      text: `ฟังเพลย์ลิสต์ "${playlistItem.name}" ที่มี ${playlistItem.songIds.length} เพลง`,
      url: shareUrl,
    })
  } catch {
    noti.info({
      title: 'ลิงก์แชร์',
      description: shareUrl,
    })
  }
}

const exportPlaylists = async () => {
  try {
    const data = exportPlaylistsData()
    const blob = new Blob([data], {
      type: 'application/json',
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    link.href = url
    link.download = `mhalong-playlists-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
    navigator.clipboard.writeText(data)
    noti.success({
      title: 'ส่งออกสำเร็จ',
      description: 'ข้อมูลเพลย์ลิสต์ถูกส่งออก และถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
    })
  } catch (error) {
    console.error('Export error:', error)
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: 'ไม่สามารถส่งออกข้อมูลได้ กรุณาลองใหม่อีกครั้ง',
    })
  }
}

const importPlaylistData = async () => {
  if (!importData.value.trim()) return

  isImporting.value = true

  try {
    const success = await importPlaylists(importData.value, mergeImport.value)

    if (success) {
      showImportModal.value = false
      importData.value = ''
      mergeImport.value = true

      noti.success({
        title: 'นำเข้าสำเร็จ',
        description: 'นำเข้าข้อมูลเพลย์ลิสต์เรียบร้อยแล้ว',
      })
    } else {
      noti.error({
        title: 'เกิดข้อผิดพลาด',
        description: 'ไม่สามารถนำเข้าข้อมูลได้ กรุณาตรวจสอบรูปแบบข้อมูลและลองใหม่อีกครั้ง',
      })
    }
  } finally {
    isImporting.value = false
  }
}

// Watch for errors
watch(error, (newError) => {
  if (newError) {
    noti.error({
      title: 'เกิดข้อผิดพลาด',
      description: newError,
    })
  }
})
</script>
