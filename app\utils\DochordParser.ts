import * as cheerio from 'cheerio'

interface ChordPosition {
  chord: string
  position: number
}

interface LyricLine {
  text: string
  chords: string[]
  chordsWithPositions: ChordPosition[]
}

interface LyricSection {
  section: string
  lines: LyricLine[]
}

interface SongData {
  id: string
  title: string
  artist: string
  artist_slug: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tuning: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  lyrics: LyricSection[]
  tags: string[]
  description: string
  youtubeId: string
  thumbnail: string
}

export class DochordParser {
  private $: cheerio.CheerioAPI
  private html: string

  constructor(html: string) {
    this.html = html
    this.$ = cheerio.load(html)
  }

  /**
   * Parse the HTML content and return structured song data
   */
  public parse(): SongData {
    return {
      id: this.extractPostId(),
      title: this.extractTitle(),
      artist: this.extractArtist(),
      artist_slug: this.extractArtistSlug(),
      genre: this.extractGenre().genre,
      genreThai: this.extractGenre().genreThai,
      difficulty: 'Medium',
      difficultyThai: 'ปานกลาง',
      key: this.extractKey(),
      tuning: this.extractTuning(),
      tempo: 120,
      timeSignature: '4/4',
      capo: 0,
      strummingPattern: '',
      chords: this.extractChords(),
      lyrics: this.extractLyrics(),
      tags: this.generateTags(),
      description: this.extractDescription(),
      youtubeId: this.extractYouTubeId(),
      thumbnail: this.extractThumbnail().url,
    }
  }

  /**
   * Extract thumbnail/cover image information
   */
  private extractThumbnail(): { url: string
    alt: string
    title: string } {
    // Priority 1: Try to extract from Twitter meta tag first
    const twitterImage = this.$('meta[name="twitter:image"]').attr('content')

    if (twitterImage) {
      return {
        url: this.normalizeImageUrl(twitterImage),
        alt: this.extractTitle() || 'Song cover',
        title: `${this.extractTitle()} - ${this.extractArtist()}`,
      }
    }

    // Priority 2: Try to extract from Open Graph meta tag
    const ogImage = this.$('meta[property="og:image"]').attr('content')

    if (ogImage) {
      return {
        url: this.normalizeImageUrl(ogImage),
        alt: this.extractTitle() || 'Song cover',
        title: `${this.extractTitle()} - ${this.extractArtist()}`,
      }
    }

    // Priority 3: Try to extract from image elements in header
    const selectors = [
      '.single-cover-header__thumbnail img',
      '.single-cover-header-thumbnail img',
      '.song-thumbnail img',
      '.post-thumbnail img',
      '.wp-post-image',
      '.cover-image img',
      '.album-cover img',
    ]

    for (const selector of selectors) {
      const imgElement = this.$(selector).first()

      if (imgElement.length > 0) {
        const url = imgElement.attr('src') || imgElement.attr('data-src') || ''
        const alt = imgElement.attr('alt') || ''
        const title = imgElement.attr('title') || ''

        if (url) {
          return {
            url: this.normalizeImageUrl(url),
            alt: alt.trim(),
            title: title.trim(),
          }
        }
      }
    }

    return {
      url: '',
      alt: '',
      title: '',
    }
  }

  /**
   * Normalize image URL (ensure it's absolute)
   */
  private normalizeImageUrl(url: string): string {
    if (!url) return ''

    // If it's already an absolute URL, return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // If it starts with //, add https:
    if (url.startsWith('//')) {
      return `https:${url}`
    }

    // If it's a relative URL, try to construct absolute URL
    if (url.startsWith('/')) {
      // Try to get base URL from canonical link
      const canonical = this.$('link[rel="canonical"]').attr('href')

      if (canonical) {
        try {
          const baseUrl = new URL(canonical).origin

          return `${baseUrl}${url}`
        } catch (e) {
          // If URL parsing fails, return the relative URL
        }
      }

      // Default to dochord.com if we can't determine base URL
      return `https://www.dochord.com${url}`
    }

    return url
  }

  /**
   * Extract post ID from various sources
   */
  private extractPostId(): string {
    // Try multiple selectors for post ID
    const articleId = this.$('article').attr('id')

    if (articleId) {
      return articleId.replace('post-', '')
    }

    // Try from hidden inputs
    const hiddenId = this.$('.p_id, #p_id, input[name="p_id"]').val() as string

    if (hiddenId) {
      return hiddenId
    }

    // Extract from URL if available
    const url = this.$('link[rel="canonical"]').attr('href') || ''
    const urlMatch = url.match(/\/(\d+)\/?$/)

    if (urlMatch) {
      return urlMatch[1]
    }

    return Date.now().toString() // Fallback to timestamp
  }

  /**
   * Extract song title with cleanup
   */
  private extractTitle(): string {
    const selectors = [
      '.single-cover-header-info__display h1',
      'h1.entry-title',
      'h1.post-title',
      'h1.song-title',
      '.page-title h1',
      'h1',
      'title',
    ]

    for (const selector of selectors) {
      const element = this.$(selector).first()

      if (element.length > 0) {
        let title = element.text().trim()

        if (!title) continue

        // Clean up common prefixes and suffixes
        title = title.replace(/^(คอร์ดเพลง|คอร์ด|เพลง|Chord|Song)\s*/i, '')
        title = title.replace(/\s*(คอร์ดเพลง|คอร์ด|Chord|Song).*$/i, '')
        title = title.replace(/\s*[-–—|]\s*(?:\S.*)?$/, '') // Remove artist after dash

        // Remove common site names
        title = title.replace(/\s*-\s*(dochord|chord|guitar).*$/i, '')

        if (title.length > 0) {
          return title.trim()
        }
      }
    }

    return 'Unknown Title'
  }

  /**
   * Extract artist name
   */
  private extractArtist(): string {
    const selectors = [
      '.single-cover-header-info-desc-item__value-link[href*="/artist/"]',
      '.artist-name',
      '.entry-meta .artist',
      'a[href*="/artist/"]',
      '.song-artist',
      '.artist-link',
      '[class*="artist"]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector).first()

      if (element.length > 0) {
        const artist = element.text().trim()

        if (artist && artist.length > 0 && !artist.match(/^(unknown|ไม่ทราบ|ไม่ระบุ)$/i)) {
          return artist
        }
      }
    }

    // Try to extract from meta tags
    const metaArtist = this.$('meta[name="artist"], meta[property="music:musician"]').attr('content')

    if (metaArtist) {
      return metaArtist.trim()
    }

    return 'Unknown Artist'
  }

  private extractArtistSlug(): string {
    const selectors = [
      '.single-cover-header-info-desc-item__value-link[href*="/artist/"]',
      'a[href*="/artist/"]',
      '.artist-link',
      '[href*="/artist/"]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector).first()

      if (element.length > 0) {
        const href = element.attr('href') || ''

        if (href.includes('/artist/')) {
          // Extract slug from URL like: /artist/three-man-down
          const artistMatch = href.match(/\/artist\/([^/?#]+)/)

          if (artistMatch && artistMatch[1]) {
            return decodeURIComponent(artistMatch[1]).trim()
          }
        }
      }
    }

    // Fallback: create slug from artist name
    const artistName = this.extractArtist()

    if (artistName && artistName !== 'Unknown Artist') {
      return artistName
    }

    return ''
  }

  /**
   * Extract genre information
   */
  private extractGenre(): { genre: string
    genreThai: string } {
    const selectors = [
      '.single-cover-header-info-desc-item__value-link[href*="/category/"]',
      'a[href*="/category/"]',
      '.genre',
      '.song-genre',
      '[class*="genre"]',
    ]

    let genreThai = ''

    for (const selector of selectors) {
      const elements = this.$(selector)

      elements.each((index, element) => {
        const text = this.$(element).text().trim()
        const href = this.$(element).attr('href') || ''

        // Check if this is a genre link and not artist
        if (href.includes('/category/') && text.length > 0 && !href.includes('/artist/')) {
          genreThai = text

          return false // Break the loop
        }
      })

      if (genreThai) break
    }

    // Try meta tags if no genre found
    if (!genreThai) {
      const metaGenre = this.$('meta[name="genre"], meta[property="music:genre"]').attr('content')

      if (metaGenre) {
        genreThai = metaGenre.trim()
      }
    }

    const genreMap: { [key: string]: string } = {
      'สตริง': 'String',
      'แร็พ': 'Rap',
      'แร๊พ': 'Rap',
      'สากล': 'International',
      'เพื่อชีวิต': 'For Life',
      'ลูกทุ่ง': 'Luk Thung',
      'ใต้': 'Southern',
      'กำเมือง': 'Kham Mueang',
      'ลาว': 'Lao',
      'ลูกกรุง': 'Luk Krung',
      'การ์ตูน': 'Cartoon',
      'ละคร': 'Drama',
      'ภาพยนตร์': 'Movie',
      'ละคร/ภาพยนตร์': 'Drama/Movie',
      'อีสาน': 'Isaan',
      'เพลงไทย': 'Thai',
      'pop': 'Pop',
      'rock': 'Rock',
      'jazz': 'Jazz',
      'blues': 'Blues',
      'country': 'Country',
      'folk': 'Folk',
      'acoustic': 'Acoustic',
      'indie': 'Indie',
      'alternative': 'Alternative',
    }

    const genre = genreMap[genreThai.toLowerCase()] || genreMap[genreThai] || 'Unknown'

    return {
      genre,
      genreThai: genreThai || 'ไม่ระบุ',
    }
  }

  /**
   * Extract song key from data-chord-key attribute
   */
  private extractKey(): string {
    // First try to get from data-chord-key attribute
    const chordKeyElement = this.$('input[data-chord-key], [data-chord-key]')

    if (chordKeyElement.length > 0) {
      const chordKeyValue = chordKeyElement.attr('data-chord-key') || chordKeyElement.val() as string

      if (chordKeyValue) {
        const keyIndex = Number.parseInt(chordKeyValue, 10)
        const mappedKey = this.mapChordKeyToNote(keyIndex)

        if (mappedKey) {
          return mappedKey
        }
      }
    }

    // Fallback to text-based extraction
    const selectors = [
      '.single-key__select-label',
      '.key-display',
      '.song-key',
      '[class*="key"]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector)

      if (element.length > 0) {
        const keyText = element.text().trim()
        const key = keyText.replace(/^(Key\s*|คีย์\s*|ประจำ\s*)/i, '').trim()

        // Validate key format
        if (key.match(/^[A-G][#b]?(m|maj|min)?$/)) {
          return key
        }
      }
    }

    // Try to extract from meta or other data attributes
    const metaKey = this.$('meta[name="key"], [data-key]').attr('content') || this.$('[data-key]').attr('data-key')

    if (metaKey && metaKey.match(/^[A-G][#b]?(m|maj|min)?$/)) {
      return metaKey
    }

    return 'C'
  }

  /**
   * Map chord key index to musical note
   */
  private mapChordKeyToNote(i: number): string {
    switch (i) {
      case 1:
        return 'C'
      case 2:
        return 'C#'
      case 3:
        return 'D'
      case 4:
        return 'Eb'
      case 5:
        return 'E'
      case 6:
        return 'F'
      case 7:
        return 'F#'
      case 8:
        return 'G'
      case 9:
        return 'G#'
      case 10:
        return 'A'
      case 11:
        return 'Bb'
      case 12:
        return 'B'
      case 13:
        return 'C' // Octave repeat
      default:
        return '' // Return empty string for invalid values
    }
  }

  /**
   * Extract tuning information from single-key__desc
   */
  private extractTuning(): string {
    const selectors = [
      '.single-key__desc',
      '.tuning-desc',
      '.tune-desc',
      '[class*="tuning"]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector)

      if (element.length > 0) {
        const tuningText = element.text().trim()

        if (tuningText && tuningText.length > 0) {
          // Clean up the tuning text
          const cleanTuning = tuningText
            .replace(/\*\*/g, '') // Remove ** markdown
            .replace(/^\s*[-•]\s*/, '') // Remove leading dash or bullet
            .trim()

          if (cleanTuning.length > 0) {
            return cleanTuning
          }
        }
      }
    }

    // Try to extract from data attributes or meta tags
    const tuningData = this.$('[data-tuning]').attr('data-tuning')
      || this.$('meta[name="tuning"]').attr('content')

    if (tuningData) {
      return tuningData.trim()
    }

    // Check if there's tuning info in key section
    const keySection = this.$('.single-key')

    if (keySection.length > 0) {
      const keyText = keySection.text()

      // Common tuning patterns
      const tuningPatterns = [
        /tune\s+down\s+[\d/]+\s*(?:step|semitone|tone)?(?:\s+to\s+[A-G][#b]?)?/i,
        /drop\s+[A-G][#b]?/i,
        /capo\s+\d+/i,
        /half\s+step\s+down/i,
        /whole\s+step\s+down/i,
        /standard\s+tuning/i,
        /alternate\s+tuning/i,
      ]

      for (const pattern of tuningPatterns) {
        const match = keyText.match(pattern)

        if (match) {
          return match[0].trim()
        }
      }
    }

    return 'Standard Tuning' // Default value
  }

  /**
   * Extract all chords used in the song
   */
  private extractChords(): string[] {
    const chords = new Set<string>()

    // Extract from chord description sections
    const descriptionSelectors = ['.des_chord', '.chord-description', '.chord-list']

    descriptionSelectors.forEach((selector) => {
      const chordDescription = this.$(selector).text()
      const chordMatches = chordDescription.match(/(คอร์ด\s+)?([A-G][^\s,\u0E00-\u0E7F]*)/g) || []

      chordMatches.forEach((match) => {
        const chord = match.replace(/คอร์ด\s+/, '').trim()

        if (chord && this.isValidChord(chord)) {
          chords.add(chord)
        }
      })
    })

    // Extract from chord elements in lyrics
    const chordSelectors = ['#c_chord', '.chord', '.c_chord', 'span[id*="chord"]', '[class*="chord"]']

    chordSelectors.forEach((selector) => {
      this.$(selector).each((index, element) => {
        const chord = this.$(element).text().trim()

        if (chord && this.isValidChord(chord)) {
          chords.add(chord)
        }
      })
    })

    // Extract from chord charts/diagrams
    this.$('.table-chord svg text, .chord-chart .chord-name, .chord-diagram .chord-name').each((index, element) => {
      const chord = this.$(element).text().trim()

      if (chord && this.isValidChord(chord)) {
        chords.add(chord)
      }
    })

    return Array.from(chords).sort()
  }

  /**
   * Validate if a string is a valid chord
   */
  private isValidChord(chord: string): boolean {
    const chordPattern = /^[A-G][#b]?([m°ø+]|maj|min|dim|aug|sus|add)?(\d+)?(\/[A-G][#b]?)?$/

    return chordPattern.test(chord)
      && chord.length >= 1
      && chord.length <= 12
      && !chord.match(/^\d+$/) // Not just numbers
      && !chord.match(/^[^A-G]/) // Must start with note
  }

  /**
   * Extract lyrics with chord positions, using <hr> tags to separate sections
   */
  private extractLyrics(): LyricSection[] {
    const lyrics: LyricSection[] = []

    // Try multiple selectors for lyrics containers
    const lyricsSelectors = [
      '.preview-chord-1, .preview-chord-2',
      '.single-entry-content-cord',
      '.lyrics-content',
      '.song-lyrics',
      '.chord-lyrics',
      '.entry-content',
    ]

    let lyricsFound = false
    const sectionCounter = 1

    for (const selector of lyricsSelectors) {
      if (lyricsFound) break

      this.$(selector).each((index, element) => {
        const sectionElement = this.$(element)

        // Process all children in order, respecting HR boundaries
        this.processElementsInOrder(sectionElement, lyrics, sectionCounter)
        lyricsFound = true
      })
    }

    return lyrics
  }

  /**
   * Process all elements in their natural DOM order
   */
  private processElementsInOrder(
    container: cheerio.Cheerio<cheerio.Element>,
    lyrics: LyricSection[],
    sectionCounter: number,
  ): void {
    let currentSection: LyricSection | null = null
    let verseCounter = 1

    container.children().each((index, element) => {
      const $elem = this.$(element)
      const tagName = element.tagName?.toLowerCase()
      const text = $elem.text().trim()

      // HR tag - reset current section
      if (tagName === 'hr') {
        currentSection = null

        return
      }

      if (!text || text.length < 2) return
      if (this.isUIElement(text)) return

      // Handle blockquotes as separate sections
      if (tagName === 'blockquote') {
        const blockquoteSection = this.createBlockquoteSection($elem, verseCounter)

        if (blockquoteSection) {
          lyrics.push(blockquoteSection)
        }

        currentSection = null // Reset after blockquote

        return
      }

      // Handle regular content (paragraphs, divs, etc.)
      if (tagName === 'p' || $elem.hasClass('lyric-line') || $elem.hasClass('line')) {
        // Create new section if needed
        if (!currentSection) {
          const sectionName = this.determineSectionName(text, verseCounter)

          currentSection = {
            section: sectionName,
            lines: [],
          }

          lyrics.push(currentSection)

          if (sectionName.includes('Verse')) {
            verseCounter++
          }
        }

        const processedLine = this.processLyricLine($elem)

        if (processedLine.text || processedLine.chords.length > 0) {
          currentSection.lines.push(processedLine)
        }
      }
    })
  }

  /**
   * Create a separate section for blockquote content
   */
  private createBlockquoteSection(
    $blockquote: cheerio.Cheerio<cheerio.Element>,
    sectionCounter: number,
  ): LyricSection | null {
    const blockquoteText = $blockquote.text().trim()

    // Determine section name for blockquote
    const sectionName = this.determineSectionName(blockquoteText, sectionCounter)

    const section: LyricSection = {
      section: sectionName,
      lines: [],
    }

    // Check if blockquote has paragraphs
    const paragraphs = $blockquote.find('p')

    if (paragraphs.length > 0) {
      // Process each paragraph in order
      paragraphs.each((pIndex, paragraph) => {
        const $p = this.$(paragraph)
        const text = $p.text().trim()

        if (!text || text.length < 2) return
        if (this.isUIElement(text)) return

        const processedLine = this.processLyricLine($p)

        if (processedLine.text || processedLine.chords.length > 0) {
          section.lines.push(processedLine)
        }
      })
    } else {
      // Handle blockquote without paragraphs (direct text content)
      const processedLine = this.processLyricLine($blockquote)

      if (processedLine.text || processedLine.chords.length > 0) {
        section.lines.push(processedLine)
      }
    }

    // Only return section if it has content
    return section.lines.length > 0 ? section : null
  }

  /**
   * Split elements by <hr> tags to identify section boundaries
   */
  private splitByHrTags(container: cheerio.Cheerio<cheerio.Element>): cheerio.Element[][] {
    const parts: cheerio.Element[][] = []
    let currentPart: cheerio.Element[] = []

    // Get all direct children including text nodes, but process only elements
    container.children().each((index, element) => {
      const tagName = element.tagName?.toLowerCase()

      if (tagName === 'hr') {
        // HR tag found - end current section and start new one
        if (currentPart.length > 0) {
          parts.push([...currentPart])
          currentPart = []
        }
      } else {
        // Add element to current section
        currentPart.push(element)
      }
    })

    // Add the last section if it has content
    if (currentPart.length > 0) {
      parts.push(currentPart)
    }

    return parts
  }

  /**
   * Check if text is a UI element that should be skipped
   */
  private isUIElement(text: string): boolean {
    const uiPatterns = [
      /^(edit|favorite|share|download|print)/i,
      /^(โหลดเพิ่ม|แชร์|ดาวน์โหลด|พิมพ์)/,
      /^(next|prev|back|home)/i,
      /^(copyright|©)/i,
      /^(advertisement|ads|สปอนเซอร์)/i,
      /^(subscribe|follow|like)/i,
      /^\d+\.$/, // Just numbers
      /^[<>{}[\]()]+$/, // Just brackets/symbols
    ]

    return uiPatterns.some((pattern) => pattern.test(text.trim()))
  }

  /**
   * Determine section name from text content
   */
  private determineSectionName(text: string, sectionCounter: number): string {
    const lowerText = text.toLowerCase()
    const thaiText = text

    // Explicit section markers with priority order
    if (lowerText.includes('intro') || thaiText.includes('อินโทร') || thaiText.includes('INTRO')) {
      return 'INTRO'
    }

    if (lowerText.includes('outro') || thaiText.includes('เอาโทร') || thaiText.includes('OUTRO')) {
      return 'OUTRO'
    }

    if (lowerText.includes('solo') || thaiText.includes('โซโล') || thaiText.includes('SOLO')) {
      return 'Solo'
    }

    if (lowerText.includes('bridge') || thaiText.includes('บริดจ์') || thaiText.includes('BRIDGE')) {
      return 'Bridge'
    }

    if (lowerText.includes('instru') || thaiText.includes('ดนตรี') || thaiText.includes('INSTRU')) {
      return 'INSTRUMENTAL'
    }

    if (thaiText.includes('ซ้ำ') || lowerText.includes('repeat') || thaiText.includes('REPEAT')) {
      return 'REPEAT'
    }

    // Chorus detection patterns
    const chorusPatterns = [
      /^\s*\*/, // Starts with *
      /chorus/i,
      /คอรัส/,
      /^hook/i,
      /^refrain/i,
    ]

    if (chorusPatterns.some((pattern) => pattern.test(text))) {
      return 'Chorus'
    }

    // Check if this is an instrumental or chord-only line
    const hasOnlyChords = text.match(/^[A-G][#b]?[^\u0E00-\u0E7F]*(\s+[A-G][#b]?[^\u0E00-\u0E7F]*)*\s*$/)

    if (hasOnlyChords) {
      // If it's just chords, it might be an intro or instrumental
      if (sectionCounter === 1) {
        return 'INTRO'
      }

      // return 'INSTRUMENTAL'
    }

    // Default progression for verses
    if (sectionCounter === 1) return 'Verse 1'
    if (sectionCounter === 2) return 'Verse 2'

    return `Verse ${sectionCounter}`
  }

  /**
   * Process a single lyric line to extract chords and positions
   */
  private processLyricLine($p: cheerio.Cheerio<cheerio.Element>): LyricLine {
    const chordsWithPositions: ChordPosition[] = []
    const lineChords: string[] = []
    let cleanText = ''

    // Try different chord selectors
    const chordSelectors = ['#c_chord', '.chord', '.c_chord', 'span[id*="chord"]', '[class*="chord"]']
    let chordElements: cheerio.Cheerio<cheerio.Element> | null = null

    for (const selector of chordSelectors) {
      chordElements = $p.find(selector)
      if (chordElements.length > 0) break
    }

    if (chordElements && chordElements.length > 0) {
      // Parse HTML to extract chords and their positions
      const html = $p.html() || ''
      let currentPosition = 0
      const tempDiv = this.$('<div>').html(html)

      // Process each node to build clean text and track chord positions
      tempDiv.contents().each((index, node) => {
        if (node.type === 'text') {
          const textContent = this.$(node).text()

          cleanText += textContent
          currentPosition += textContent.length
        } else if (node.type === 'tag') {
          const $node = this.$(node)
          const isChordElement = chordSelectors.some((sel) => {
            const selectorName = sel.replace(/^[#.]/, '')

            return $node.is(selectorName) || $node.attr('id') === selectorName || $node.hasClass(selectorName)
          })

          if (isChordElement) {
            const chord = $node.text()

            if (chord && this.isValidChord(chord)) {
              lineChords.push(chord)
              chordsWithPositions.push({
                chord: chord,
                position: currentPosition,
              })
            }
          } else {
            const textContent = $node.text()

            cleanText += textContent
            currentPosition += textContent.length
          }
        }
      })
    } else {
      // No chord elements found, just get plain text
      cleanText = $p.text()
    }

    // Clean up text but keep the * marker for chorus
    cleanText = cleanText.replace(/^\d+\.\s*/, '') // Remove numbering

    return {
      text: cleanText,
      chords: lineChords,
      chordsWithPositions: chordsWithPositions,
    }
  }

  /**
   * Extract YouTube video ID
   */
  private extractYouTubeId(): string {
    const selectors = [
      '.perfmatters-lazy-youtube',
      'iframe[src*="youtube"]',
      'iframe[data-src*="youtube"]',
      'a[href*="youtube"]',
      'a[href*="youtu.be"]',
      '[data-youtube-id]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector).first()

      if (element.length > 0) {
        // Try data attribute first
        const dataId = element.attr('data-youtube-id')
        if (dataId) return dataId

        // Extract from URL
        const src = element.attr('data-src') || element.attr('src') || element.attr('href') || ''
        const patterns = [
          /(?:embed\/|watch\?v=|youtu\.be\/|v\/|e\/|watch\?.*&v=)([\w-]{11})/,
          /youtube\.com\/watch\?v=([\w-]{11})/,
          /youtu\.be\/([\w-]{11})/,
        ]

        for (const pattern of patterns) {
          const match = src.match(pattern)
          if (match) return match[1]
        }
      }
    }

    return ''
  }

  /**
   * Extract song description
   */
  private extractDescription(): string {
    const selectors = [
      '.archive-desc',
      '.song-description',
      '.entry-summary',
      '.description',
      'meta[name="description"]',
      'meta[property="og:description"]',
    ]

    for (const selector of selectors) {
      const element = this.$(selector)

      if (element.length > 0) {
        const desc = element.attr('content') || element.text()

        if (desc && desc.trim().length > 10) {
          return desc.trim()
        }
      }
    }

    return ''
  }

  /**
   * Generate tags based on song content
   */
  private generateTags(): string[] {
    const tags: string[] = []
    const genre = this.extractGenre()
    const artist = this.extractArtist()
    const key = this.extractKey()

    if (artist && artist !== 'Unknown Artist') tags.push(artist)
    if (genre.genreThai && genre.genreThai !== 'ไม่ระบุ') tags.push(genre.genreThai)
    if (key && key !== 'C') tags.push(`Key ${key}`)

    // Add common tags
    tags.push('คอร์ดเพลง', 'กีตาร์', 'เพลง')

    // Add genre-specific tags
    const genreLower = genre.genreThai.toLowerCase()
    if (genreLower.includes('สตริง')) tags.push('acoustic', 'fingerpicking')
    if (genreLower.includes('แร็พ') || genreLower.includes('แร๊พ')) tags.push('hip-hop', 'rap')
    if (genreLower.includes('สากล')) tags.push('international', 'cover')
    if (genreLower.includes('ลูกทุ่ง')) tags.push('country', 'folk')
    if (genreLower.includes('เพื่อชีวิต')) tags.push('folk', 'indie')

    return [...new Set(tags)] // Remove duplicates
  }

  /**
   * Static method for quick parsing
   */
  static parse(html: string): SongData {
    const parser = new DochordParser(html)

    return parser.parse()
  }
}

// Export interfaces for external use
export type { SongData, LyricSection, LyricLine, ChordPosition }

// Example usage:
// const parser = new DochordParser(htmlContent);
// const songData = parser.parse();
//
// Or use static method:
// const songData = DochordParser.parse(htmlContent);
