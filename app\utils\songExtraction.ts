import * as cheerio from 'cheerio'

export interface SongData {
  id: string
  title: string
  artist: string
  genre: string
  genreThai: string
  difficulty: string
  difficultyThai: string
  key: string
  tempo: number
  timeSignature: string
  capo: number
  strummingPattern: string
  chords: string[]
  lyrics: {
    section: string
    lines: {
      text: string
      chords: string[]
      chordsWithPositions?: {
        chord: string
        position: number
      }[]
    }[]
  }[]
  tags: string[]
  description: string
  youtubeId?: string
  duration?: string
  releaseYear?: number
  album?: string
  url?: string
  image?: string
  content?: string
  slug?: string
  date?: string
  copyright?: string
  tips?: string[]
  relatedSongs?: any[]
}

export interface WebScrapedData {
  lyrics: SongData['lyrics']
  chords: string[]
  key: string
}

/**
 * Song extraction utility class
 */
export class SongExtractor {
  /**
   * Extract key from content using various patterns
   */
  extractKeyFromContent = (content: string): string => {
    const keyPatterns = [
      /คีย์\s*(?::\s*)?([A-G][#b]?m?)/i,
      /key\s*(?::\s*)?([A-G][#b]?m?)/i,
      /ทำนอง\s*([A-G][#b]?m?)/i,
    ]

    for (const pattern of keyPatterns) {
      const match = content.match(pattern)

      if (match) {
        return match[1]
      }
    }

    return 'C'
  }

  /**
   * Extract capo position from content
   */
  extractCapoFromContent = (content: string): number => {
    const capoPatterns = [
      /capo\s*(?::\s*)?(\d+)/i,
      /คาโป\s*(?::\s*)?(\d+)/,
      /เฟรต\s*(\d+)/,
    ]

    for (const pattern of capoPatterns) {
      const match = content.match(pattern)

      if (match) {
        return Number.parseInt(match[1], 10)
      }
    }

    return 0
  }

  /**
   * Extract chords from content with improved pattern matching
   */
  extractChordsFromContent = (content: string): string[] => {
    // Improved regex pattern to capture longest chords first
    const chordPattern = /\b([A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|add13|add11|add9|7sus4|9sus4|sus4|sus2|maj|min|dim|aug|sus|add|13|11|[976m])?)\b/g

    const matches = content.match(chordPattern) || []

    // Filter valid chords and remove duplicates
    const validChords = matches.filter((chord) => {
      const validChordPattern = /^[A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|add13|add11|add9|7sus4|9sus4|sus4|sus2|maj|min|dim|aug|sus|add|13|11|[976m])?$/

      return validChordPattern.test(chord)
    })

    // Remove chords that are substrings of other chords
    const uniqueChords = validChords.filter((chord, index, arr) => {
      const isSubchord = arr.some((otherChord, otherIndex) => {
        return otherIndex !== index
          && otherChord.length > chord.length
          && otherChord.startsWith(chord)
          && otherChord !== chord
      })

      return !isSubchord
    })

    const finalChords = [...new Set(uniqueChords)]

    // Return basic chords if none found
    return finalChords.length > 0 ? finalChords : ['C', 'G', 'Am', 'F']
  }

  /**
   * Remove chords from text
   */
  removeChords = (text: string): string => {
    if (!text || typeof text !== 'string') {
      return ''
    }

    const chordPattern = /\b[A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|add13|add11|add9|7sus4|9sus4|sus4|sus2|maj|min|dim|aug|sus|add|13|11|[976m])?(?:\/[A-G][#b]?)?\b/g

    let cleanText = text.replace(chordPattern, '')

    cleanText = cleanText
      .replace(/\s+/g, ' ')
      .replace(/\|\s*\|/g, '|')
      .replace(/^\s*\|\s*/, '')
      .replace(/\s*\|\s*$/, '')
      .replace(/\s*\|\s*/g, ' ')
      .trim()

    return cleanText
  }

  /**
   * Remove chords from lyrics data structure
   */
  removeChordsFromLyrics = (lyrics: SongData['lyrics']): SongData['lyrics'] => {
    return lyrics.map((section) => ({
      ...section,
      lines: section.lines.map((line) => ({
        ...line,
        text: this.removeChords(line.text),
      })),
    }))
  }

  /**
   * Extract lyrics from HTML content
   */
  extractLyricsFromContent = (content: string): SongData['lyrics'] => {
    if (!content) {
      return []
    }

    try {
      const $ = cheerio.load(content)
      const sections: SongData['lyrics'] = []
      const allChords = new Set<string>()

      // Focus on the main song content area
      const songContent = $('.single-entry-content-cord')

      if (songContent.length === 0) {
        console.warn('No .single-entry-content-cord found, falling back to full content extraction')

        // Fallback to full content if specific class not found
        return this.extractLyricsFromFullContent($, sections, allChords)
      }

      console.log('Extracting lyrics from .single-entry-content-cord')

      // Split song content by <hr> tags or use entire content as one section
      let sectionParts: string[] = []
      const songHtml = songContent.html() || ''
      const hrParts = songHtml.split(/<hr[^>]*>/i).filter((part) => part.trim()) || []

      if (hrParts.length <= 1) {
        sectionParts = [songHtml]
      } else {
        sectionParts = hrParts
      }

      for (let partIndex = 0; partIndex < sectionParts.length; partIndex++) {
        const part = sectionParts[partIndex].trim()
        if (!part) continue

        const $part = cheerio.load(part)

        // Convert .cv-th elements to line breaks
        let cvThFound = false

        $part('.cv-th').each((_, element) => {
          const $element = $part(element)
          const text = $element.text()

          $element.replaceWith('\n' + text + '\n')
          cvThFound = true
        })

        // Try alternative methods if .cv-th not found
        if (!cvThFound) {
          $part('br').replaceWith('\n')
          $part('p').each((_, element) => {
            const $element = $part(element)
            const text = $element.text()

            $element.replaceWith('\n' + text + '\n')
          })

          $part('div').each((_, element) => {
            const $element = $part(element)
            const text = $element.text()

            if (text.trim()) {
              $element.replaceWith('\n' + text + '\n')
            }
          })
        }

        const partContent = $part.text()
          .replace(/\n\s+/g, '\n')
          .replace(/\s+\n/g, '\n')
          .replace(/\n{3,}/g, '\n\n')
          .replace(/\s+/g, ' ')
          .trim()

        if (!partContent) continue

        // Filter out unwanted content like "Edit Favorite"
        if (partContent.includes('Edit Favorite')) continue

        const partLines = partContent.split('\n').filter((line) => line.trim())
        const sectionLines: SongData['lyrics'][0]['lines'] = []
        let sectionName = `Section ${partIndex + 1}`

        // Check first line for section name
        if (partLines.length > 0) {
          const firstLine = partLines[0].trim()

          if (firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:/i)) {
            const match = firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:\s*(.*)/i)

            if (match) {
              sectionName = match[1]

              if (match[2]) {
                const chords = match[2].split('|').filter((c: string) => c.trim())

                chords.forEach((chord: string) => allChords.add(chord.trim()))
                sectionLines.push({
                  text: '',
                  chords: chords,
                })
              }

              partLines.shift()
            }
          } else if (partIndex === 0) {
            sectionName = 'Verse'
          } else if (firstLine.includes('*') || firstLine.toLowerCase().includes('chorus')) {
            sectionName = 'Chorus'
          } else {
            sectionName = 'Verse'
          }
        }

        // Process remaining lines
        for (let i = 0; i < partLines.length; i++) {
          const line = partLines[i].trim()
          const chordPattern = /[A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|7sus4|9sus4|add13|add11|add9|sus4|sus2|maj|min|dim|aug|sus|add|[m796]|11|13)?/g
          const chordMatches = line.match(chordPattern) || []

          chordMatches.forEach((chord: string) => allChords.add(chord))

          const onlyChords = line.match(/^[\s|A-G#0-9mjsuin]+$/i)

          if (onlyChords && chordMatches.length > 0) {
            sectionLines.push({
              text: '',
              chords: [...new Set(chordMatches)],
            })
          } else {
            const processedText = line
              .replace(/\s+/g, ' ')
              .replace(/\|/g, ' ')
              .trim()

            sectionLines.push({
              text: processedText,
              chords: [...new Set(chordMatches)],
            })
          }
        }

        if (sectionLines.length > 0) {
          sections.push({
            section: sectionName,
            lines: sectionLines,
          })
        }
      }

      return sections.length > 0
        ? sections
        : [
          {
            section: 'Intro',
            lines: [
              {
                text: 'ไม่สามารถแยกเนื้อเพลงได้',
                chords: [],
              },
            ],
          },
        ]
    } catch (error) {
      console.error('Error extracting lyrics from content:', error)

      return [
        {
          section: 'Intro',
          lines: [
            {
              text: 'เกิดข้อผิดพลาดในการแยกเนื้อเพลง',
              chords: [],
            },
          ],
        },
      ]
    }
  }

  /**
   * Fallback function to extract lyrics from full content when .single-entry-content-cord is not found
   */
  private extractLyricsFromFullContent = ($: any, sections: SongData['lyrics'], allChords: Set<string>): SongData['lyrics'] => {
    // Handle blockquotes for intro/solo sections
    const blockquotes = $('blockquote')

    blockquotes.each((_: any, element: any) => {
      const $blockquote = $(element)
      const blockquoteText = $blockquote.text().trim()

      if (blockquoteText.match(/^(INTRO|SOLO|INSTRU)\s*:/i)) {
        const match = blockquoteText.match(/^(INTRO|SOLO|INSTRU)\s*:\s*(.*)/i)

        if (match) {
          const chords = match[2].split(/[|\s]+/).filter((c: string) => c.trim() && /^[A-G][#b]?/.test(c))

          chords.forEach((chord: string) => allChords.add(chord.trim()))
          sections.push({
            section: match[1],
            lines: [{
              text: '',
              chords: chords,
            }],
          })
        }
      }

      $blockquote.remove()
    })

    // Split content by <hr> tags or use entire content as one section
    let sectionParts: string[] = []
    const hrParts = $.html()?.split(/<hr[^>]*>/i).filter((part: string) => part.trim()) || []

    if (hrParts.length <= 1) {
      sectionParts = [$.html() || '']
    } else {
      sectionParts = hrParts
    }

    for (let partIndex = 0; partIndex < sectionParts.length; partIndex++) {
      const part = sectionParts[partIndex].trim()
      if (!part) continue

      const $part = cheerio.load(part)

      // Convert .cv-th elements to line breaks
      let cvThFound = false

      $part('.cv-th').each((_: any, element: any) => {
        const $element = $part(element)
        const text = $element.text()

        $element.replaceWith('\n' + text + '\n')
        cvThFound = true
      })

      // Try alternative methods if .cv-th not found
      if (!cvThFound) {
        $part('br').replaceWith('\n')
        $part('p').each((_: any, element: any) => {
          const $element = $part(element)
          const text = $element.text()

          $element.replaceWith('\n' + text + '\n')
        })

        $part('div').each((_: any, element: any) => {
          const $element = $part(element)
          const text = $element.text()

          if (text.trim()) {
            $element.replaceWith('\n' + text + '\n')
          }
        })
      }

      const partContent = $part.text()
        .replace(/\n\s+/g, '\n')
        .replace(/\s+\n/g, '\n')
        .replace(/\n{3,}/g, '\n\n')
        .replace(/\s+/g, ' ')
        .trim()

      if (!partContent) continue

      // Filter out unwanted content like "Edit Favorite"
      if (partContent.includes('Edit Favorite')) continue

      const partLines = partContent.split('\n').filter((line) => line.trim())
      const sectionLines: SongData['lyrics'][0]['lines'] = []
      let sectionName = 'Verse'

      // Check first line for section name
      if (partLines.length > 0) {
        const firstLine = partLines[0].trim()

        if (firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:/i)) {
          const match = firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:\s*(.*)/i)

          if (match) {
            sectionName = match[1]

            if (match[2]) {
              const chords = match[2].split('|').filter((c: string) => c.trim())

              chords.forEach((chord: string) => allChords.add(chord.trim()))
              sectionLines.push({
                text: '',
                chords: chords,
              })
            }

            partLines.shift()
          }
        } else if (firstLine.includes('*') || firstLine.toLowerCase().includes('chorus')) {
          sectionName = 'Chorus'
        } else {
          sectionName = 'Verse'
        }
      }

      // Process remaining lines
      for (let i = 0; i < partLines.length; i++) {
        const line = partLines[i].trim()

        // Skip unwanted lines
        if (line.includes('Edit Favorite')) continue

        const chordPattern = /[A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|7sus4|9sus4|add13|add11|add9|sus4|sus2|maj|min|dim|aug|sus|add|[m796]|11|13)?/g
        const chordMatches = line.match(chordPattern) || []

        chordMatches.forEach((chord: string) => allChords.add(chord))

        const onlyChords = line.match(/^[\s|A-G#0-9mjsuin]+$/i)

        if (onlyChords && chordMatches.length > 0) {
          sectionLines.push({
            text: '',
            chords: [...new Set(chordMatches)],
          })
        } else {
          const processedText = line
            .replace(/\s+/g, ' ')
            .replace(/\|/g, ' ')
            .trim()

          sectionLines.push({
            text: processedText,
            chords: [...new Set(chordMatches)],
          })
        }
      }

      if (sectionLines.length > 0) {
        sections.push({
          section: sectionName,
          lines: sectionLines,
        })
      }
    }

    return sections
  }

  /**
   * Extract tips from content
   */
  extractTipsFromContent = (content: string): string[] => {
    return [
      'เทคนิคการเล่นจะถูกแยกจาก content ของ dochord.com',
    ]
  }

  /**
   * Extract data from web scraped content with chord positions
   */
  extractFromWebContent = (webContent: string): WebScrapedData => {
    try {
      if (!webContent) {
        return {
          lyrics: [],
          chords: [],
          key: 'C',
        }
      }

      const $ = cheerio.load(webContent)
      const sections: SongData['lyrics'] = []
      const allChords = new Set<string>()
      let detectedKey = 'C'

      // Handle blockquotes for intro/solo sections
      const blockquotes = $('blockquote')

      blockquotes.each((_, element) => {
        const $blockquote = $(element)
        const blockquoteText = $blockquote.text().trim()

        if (blockquoteText.match(/^(INTRO|SOLO|INSTRU)\s*:/i)) {
          const match = blockquoteText.match(/^(INTRO|SOLO|INSTRU)\s*:\s*(.*)/i)

          if (match) {
            const chords = match[2].split(/[|\s]+/).filter((c) => c.trim() && /^[A-G][#b]?/.test(c))

            chords.forEach((chord: string) => allChords.add(chord.trim()))
            sections.push({
              section: match[1],
              lines: [{
                text: '',
                chords: chords,
              }],
            })
          }
        }

        $blockquote.remove()
      })

      // Split sections by <hr> tag
      const sectionParts = $.html()?.split(/<hr[^>]*>/i).filter((part) => part.trim()) || []

      for (let partIndex = 0; partIndex < sectionParts.length; partIndex++) {
        const part = sectionParts[partIndex].trim()
        if (!part) continue

        const $part = cheerio.load(part)

        // Convert .cv-th to line breaks
        $part('.cv-th').each((_, element) => {
          $part(element).replaceWith('\n' + $part(element).text())
        })

        const partContent = $part.text()
          .replace(/\n\s+/g, '\n')
          .replace(/\s+\n/g, '\n')
          .replace(/\s+/g, ' ')
          .trim()

        if (!partContent) continue

        const partLines = partContent.split('\n').filter((line) => line.trim())
        const sectionLines: SongData['lyrics'][0]['lines'] = []
        let sectionName = `Section ${partIndex + 1}`

        // Check first line for section name
        if (partLines.length > 0) {
          const firstLine = partLines[0].trim()

          if (firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:/i)) {
            const match = firstLine.match(/^(INTRO|INSTRU|VERSE|CHORUS|BRIDGE|OUTRO)\s*:\s*(.*)/i)

            if (match) {
              sectionName = match[1]

              if (match[2]) {
                const chords = match[2].split('|').filter((c: string) => c.trim())

                chords.forEach((chord: string) => allChords.add(chord.trim()))
                sectionLines.push({
                  text: '',
                  chords: chords,
                })
              }

              partLines.shift()
            }
          } else if (partIndex === 0) {
            sectionName = 'Intro'
          } else if (firstLine.includes('*') || firstLine.toLowerCase().includes('chorus')) {
            sectionName = 'Chorus'
          } else {
            sectionName = 'Verse'
          }
        }

        // Process remaining lines
        for (let i = 0; i < partLines.length; i++) {
          const line = partLines[i].trim()

          // Check for key information
          const keyMatch = line.match(/Key\s+([A-G][#b]?)/i)

          if (keyMatch) {
            detectedKey = keyMatch[1]
            continue
          }

          const chordPattern = /[A-G][#b]?(?:maj13|maj11|maj9|maj7|m13|m11|m9|m7b5|mMaj7|dim7|aug7|7sus4|9sus4|add13|add11|add9|sus4|sus2|maj|min|dim|aug|sus|add|[m796]|11|13)?/g
          const chordMatches = line.match(chordPattern) || []

          chordMatches.forEach((chord: string) => allChords.add(chord))

          const onlyChords = line.match(/^[\s|A-G#0-9mjsuin]+$/i)

          if (onlyChords && chordMatches.length > 0) {
            sectionLines.push({
              text: '',
              chords: [...new Set(chordMatches)],
            })
          } else {
            // Track chord positions
            const chordsWithPositions: { chord: string
              position: number }[] = []

            let match
            const chordRegex = new RegExp(chordPattern.source, 'g')

            while ((match = chordRegex.exec(line)) !== null) {
              chordsWithPositions.push({
                chord: match[0],
                position: match.index,
              })
            }

            const processedText = line
              .replace(/\s+/g, ' ')
              .replace(/\|/g, ' ')
              .trim()

            sectionLines.push({
              text: processedText,
              chords: [...new Set(chordMatches)],
              chordsWithPositions: chordsWithPositions,
            })
          }
        }

        if (sectionLines.length > 0) {
          sections.push({
            section: sectionName,
            lines: sectionLines,
          })
        }
      }

      return {
        lyrics: sections,
        chords: Array.from(allChords),
        key: detectedKey,
      }
    } catch (error) {
      console.error('Error extracting from web content:', error)

      return {
        lyrics: [],
        chords: [],
        key: 'C',
      }
    }
  }
}

// Export backward compatibility functions and create default instance
const songExtractor = new SongExtractor()

export const extractKeyFromContent = songExtractor.extractKeyFromContent

export const extractCapoFromContent = songExtractor.extractCapoFromContent

export const extractChordsFromContent = songExtractor.extractChordsFromContent

export const removeChords = songExtractor.removeChords

export const removeChordsFromLyrics = songExtractor.removeChordsFromLyrics

export const extractLyricsFromContent = songExtractor.extractLyricsFromContent

export const extractTipsFromContent = songExtractor.extractTipsFromContent

export const extractFromWebContent = songExtractor.extractFromWebContent
