# Search API Implementation

## Overview
The search API has been updated to support web scraping from dochord.com search page when a search query is provided, while maintaining backward compatibility with the existing WordPress API for other use cases.

## Implementation Details

### File Modified
- `server/api/songs/search.get.ts`

### Dependencies Added
- `puppeteer` - For web scraping functionality

### API Behavior

The API now handles three different scenarios:

1. **Search Query with Web Scraping** (`?q=searchterm`)
   - Uses Puppeteer to scrape search results from dochord.com search page
   - Extracts song information from Google Custom Search results
   - Parses song titles and artists from the search results
   - Returns structured data with song ID, title, artist, thumbnail, and URL

2. **Include Parameter** (`?include=123,456`)
   - Uses the original WordPress API to fetch specific posts by ID
   - Maintains existing functionality for playlist loading

3. **Default Listing** (no parameters)
   - Uses the original WordPress API to fetch recent posts
   - Maintains existing functionality for homepage listing

### Search URL Format
The scraping targets URLs in this format:
```
https://www.dochord.com/search/?q={encoded_query}#gsc.tab=0&gsc.q={encoded_query}&gsc.page={page}
```

### Data Extraction
From each search result, the scraper extracts:
- **ID**: Extracted from the dochord.com URL (e.g., `/378742/` → `378742`)
- **Title**: Cleaned from the page title (removes "คอร์ดเพลง " and " | dochord.com")
- **Artist**: Parsed from the title (assumes last word is artist name)
- **Thumbnail**: Image URL from search results
- **URL**: Direct link to the song page

### Response Format
```json
{
  "count": 10,
  "limit": 30,
  "page": 1,
  "total": 50,
  "items": [
    {
      "id": "378742",
      "title": "เจิดจรัส",
      "artist": "YOUNGOHM",
      "artist_slug": "youngohm",
      "thumbnail": "https://example.com/thumb.jpg",
      "url": "https://www.dochord.com/378742/"
    }
  ]
}
```

### Error Handling
- Puppeteer launch failures return 500 error
- Search timeout failures return 500 error
- Falls back to original API behavior for non-search requests

### Performance Considerations
- Puppeteer browser instances are properly closed after each request
- Uses headless mode for better performance
- Includes timeout settings to prevent hanging requests
- Uses `networkidle2` wait condition for reliable page loading

### Testing
Manual testing confirmed the scraping functionality works correctly:
- Successfully extracts search results from dochord.com
- Properly parses song titles and artist names
- Returns structured data in the expected format
- Handles Thai language content correctly

## Usage Examples

### Search for songs
```
GET /api/songs/search?q=เธอเหมือนกับแสงไฟ
```

### Get specific songs by ID
```
GET /api/songs/search?include=378742,371348
```

### Get recent songs
```
GET /api/songs/search?page=1&limit=30
```
