{"name": "chords.mhalong.com", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev -o", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --quiet", "lint:fix": "eslint --fix . --quiet", "prepare": "husky", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui"}, "dependencies": {"@finema/core": "^2.26.5", "cheerio": "^1.1.0", "html2canvas": "^1.4.1", "nuxt": "^4.0.1", "puppeteer": "^24.15.0", "vue": "^3.5.14", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify-json/lucide": "^1.2.53", "@iconify-json/tdesign": "^1.2.8", "@nuxt/eslint-config": "^1.4.1", "eslint": "^9.26.0", "eslint-plugin-better-tailwindcss": "^3.4.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^16.0.0"}, "lint-staged": {"*": "eslint --fix . --quiet"}, "overrides": {"@vercel/nft": "^0.27.4"}}