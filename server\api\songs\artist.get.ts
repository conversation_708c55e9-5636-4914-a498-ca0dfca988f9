import axios from 'axios'

interface SongData {
  id: string
  title: string
  artist: string
  artist_slug: string
  thumbnail: string
}

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string, 10) || 1
  const limit = Number.parseInt(query.limit as string, 10) || 30

  try {
    const {
      data: temp,
    } = await axios.get<any[]>('https://dochord.com/wp-json/wp/v2/artist', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
      },
      params: {
        slug: searchQuery,
        per_page: 1,
        page: 1,
      },
    })

    if (!temp || !temp.length) {
      event.node.res.statusCode = 500

      return {
        code: '500',
        message: 'Failed to fetch ',
      }
    }

    const res = await axios.get<any[]>('https://dochord.com/wp-json/wp/v2/posts', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
      },
      params: {
        artist: temp[0].id,
        per_page: limit,
        page: page,
        _embed: true,
      },
    })

    return {
      count: res.data.length,
      limit: limit,
      page: page,
      total: Number.parseInt(res.headers['x-wp-total'] as string, 10) || res.data.length,
      items: res.data.map((post: any) => {
        const title = post.title?.rendered || ''

        // ข้อมูลอินจาก _embedded.wp:term
        const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
          termGroup.some((term: any) => term.taxonomy === 'artist'),
        )?.find((term: any) => term.taxonomy === 'artist')

        return {
          id: post.id.toString(),
          title: title.replace(/<[^>]*>/g, ''),
          artist: artistInfo?.name || 'Unknown Artist',
          artist_slug: decodeURI(artistInfo?.slug || ''),
          thumbnail: post._embedded?.['wp:featuredmedia']?.[0]?.source_url || '',
        }
      }),
    }
  } catch (error) {
    console.error('Error fetching from dochord.com:', error)
    event.node.res.statusCode = 500

    return {
      code: '500',
      message: 'Failed to fetch ',
    }
  }
})
