import axios from 'axios'
import puppeteer from 'puppeteer'

interface SearchResult {
  id: string
  title: string
  artist: string
  artist_slug: string
  thumbnail: string
  url: string
}

const scrapeSearchResults = async (searchQuery: string, page: number = 1): Promise<SearchResult[]> => {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  })

  try {
    const browserPage = await browser.newPage()

    await browserPage.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

    // สร้าง URL สำหรับค้นหา
    const encodedQuery = encodeURIComponent(searchQuery)
    const searchUrl = `https://www.dochord.com/search/?q=${encodedQuery}#gsc.tab=0&gsc.q=${encodedQuery}&gsc.page=${page}`

    await browserPage.goto(searchUrl, {
      waitUntil: 'networkidle2',
      timeout: 30000,
    })

    // รอให้ผลการค้นหาโหลด
    await browserPage.waitForSelector('.gsc-webResult', {
      timeout: 10000,
    })

    // ดึงข้อมูลจากผลการค้นหา
    const results = await browserPage.evaluate(() => {
      const searchResults = []
      const resultElements = document.querySelectorAll('.gsc-webResult')

      resultElements.forEach((element) => {
        const titleElement = element.querySelector('.gs-title a')
        const urlElement = element.querySelector('.gs-title a')
        const thumbnailElement = element.querySelector('.gs-image img')

        if (titleElement && urlElement) {
          const title = titleElement.textContent || ''
          const url = urlElement.getAttribute('href') || ''
          const thumbnail = thumbnailElement?.getAttribute('src') || ''

          // ดึง ID จาก URL (เช่น https://www.dochord.com/378742/ -> 378742)
          const idMatch = url.match(/\/(\d+)\/?$/)
          const id = idMatch ? idMatch[1] : ''

          // แยกชื่อเพลงและศิลปิน
          let songTitle = title.replace('คอร์ดเพลง ', '').replace(' | dochord.com', '')
          let artist = 'Unknown Artist'

          // พยายามแยกศิลปินจากชื่อเพลง
          const artistMatch = songTitle.match(/^(.+?)\s+(.+)$/)

          if (artistMatch && artistMatch.length >= 3) {
            // ถ้าพบรูปแบบ "ชื่อเพลง ศิลปิน"
            const parts = songTitle.split(' ')

            if (parts.length >= 2) {
              artist = parts[parts.length - 1]
              songTitle = parts.slice(0, -1).join(' ')
            }
          }

          if (id && songTitle) {
            searchResults.push({
              id,
              title: songTitle,
              artist,
              artist_slug: artist.toLowerCase().replace(/\s+/g, '-'),
              thumbnail,
              url,
            })
          }
        }
      })

      return searchResults
    })

    return results
  } finally {
    await browser.close()
  }
}

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const searchQuery = (query.q as string) || ''
  const page = Number.parseInt(query.page as string, 10) || 1
  const limit = Number.parseInt(query.limit as string, 10) || 30
  const include = (query.include as string) || ''

  // ถ้ามี include parameter ให้ใช้ API เดิม
  if (include) {
    const params = {
      include: include,
      _embed: true,
    }

    try {
      const response = await axios.get('https://www.dochord.com/wp-json/wp/v2/posts', {
        params: params,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      })

      const transformedSongs = response.data.map((post: any) => {
        const title = post.title?.rendered || ''
        const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
          termGroup.some((term: any) => term.taxonomy === 'artist'),
        )?.find((term: any) => term.taxonomy === 'artist')

        const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

        return {
          id: post.id.toString(),
          title: title.replace(/<[^>]*>/g, ''),
          artist: artistInfo?.name || 'Unknown Artist',
          artist_slug: artistInfo?.slug || '',
          thumbnail: featuredImage,
        }
      })

      return {
        count: transformedSongs.length,
        limit: limit,
        page: page,
        total: transformedSongs.length,
        items: transformedSongs.sort((a: any, b: any) => {
          return include.indexOf(a.id) - include.indexOf(b.id)
        }),
      }
    } catch (error) {
      event.node.res.statusCode = 500
      console.error('Error fetching from dochord.com API:', error)

      return {
        code: '500',
        message: 'Failed to fetch from API',
      }
    }
  }

  // ถ้ามี searchQuery ให้ใช้ web scraping
  if (searchQuery) {
    try {
      const scrapedResults = await scrapeSearchResults(searchQuery, page)

      // จำกัดจำนวนผลลัพธ์ตาม limit
      const limitedResults = scrapedResults.slice(0, limit)

      return {
        count: limitedResults.length,
        limit: limit,
        page: page,
        total: scrapedResults.length,
        items: limitedResults,
      }
    } catch (error) {
      event.node.res.statusCode = 500
      console.error('Error scraping search results:', error)

      return {
        code: '500',
        message: 'Failed to scrape search results',
      }
    }
  }

  // ถ้าไม่มี searchQuery และ include ให้ใช้ API เดิม
  try {
    const params = {
      _embed: true,
      per_page: limit,
      page: page,
    }

    const response = await axios.get('https://www.dochord.com/wp-json/wp/v2/posts', {
      params: params,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    })

    const transformedSongs = response.data.map((post: any) => {
      const title = post.title?.rendered || ''
      const artistInfo = post._embedded?.['wp:term']?.find((termGroup: any[]) =>
        termGroup.some((term: any) => term.taxonomy === 'artist'),
      )?.find((term: any) => term.taxonomy === 'artist')

      const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url || ''

      return {
        id: post.id.toString(),
        title: title.replace(/<[^>]*>/g, ''),
        artist: artistInfo?.name || 'Unknown Artist',
        artist_slug: artistInfo?.slug || '',
        thumbnail: featuredImage,
      }
    })

    const totalCount = Number.parseInt(response.headers['x-wp-total'] as string, 10) || transformedSongs.length

    return {
      count: transformedSongs.length,
      limit: limit,
      page: page,
      total: totalCount,
      items: transformedSongs,
    }
  } catch (error) {
    event.node.res.statusCode = 500
    console.error('Error fetching from dochord.com:', error)

    return {
      code: '500',
      message: 'Failed to fetch',
    }
  }
})
