import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock the storage composable
const mockStorage = {
  loadPlaylists: vi.fn(),
  createPlaylist: vi.fn(),
  updatePlaylist: vi.fn(),
  deletePlaylist: vi.fn(),
  addSongToPlaylist: vi.fn(),
  removeSongFromPlaylist: vi.fn(),
  reorderPlaylistSongs: vi.fn(),
  exportPlaylists: vi.fn(),
  importPlaylists: vi.fn(),
  clearAllPlaylists: vi.fn(),
  isStorageAvailable: vi.fn(() => true),
}

vi.mock('~/composables/usePlaylistStorage', () => ({
  usePlaylistStorage: () => mockStorage,
}))

// Mock Vue composables
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')

  return {
    ...actual,
    ref: vi.fn((value) => ({
      value,
    })),
    computed: vi.fn((fn) => ({
      value: fn(),
    })),
    readonly: vi.fn((value) => value),
    onMounted: vi.fn(),
  }
})

describe('usePlaylist', () => {
  let usePlaylist: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // Reset mock implementations
    mockStorage.loadPlaylists.mockReturnValue({
      version: '1.0.0',
      playlists: [],
      lastUpdated: '2024-01-01T00:00:00.000Z',
    })

    // Import the composable after mocks are set up
    const module = await import('~/composables/usePlaylist')

    usePlaylist = module.usePlaylist
  })

  describe('initialization', () => {
    it('should initialize with empty playlists', () => {
      const playlist = usePlaylist()

      expect(playlist.playlists.value).toEqual([])
      expect(playlist.isLoading.value).toBe(false)
      expect(playlist.error.value).toBeNull()
    })
  })

  describe('createPlaylist', () => {
    it('should create a new playlist successfully', async () => {
      const newPlaylist = {
        id: 'test-id',
        name: 'Test Playlist',
        songIds: [],
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      }

      mockStorage.createPlaylist.mockReturnValue(newPlaylist)

      const playlist = usePlaylist()
      const result = await playlist.createPlaylist('Test Playlist')

      expect(result).toEqual(newPlaylist)
      expect(mockStorage.createPlaylist).toHaveBeenCalledWith('Test Playlist')
    })

    it('should handle creation failure', async () => {
      mockStorage.createPlaylist.mockReturnValue(null)

      const playlist = usePlaylist()
      const result = await playlist.createPlaylist('Test Playlist')

      expect(result).toBeNull()
      expect(playlist.error.value).toBe('Failed to create playlist')
    })
  })

  describe('addSongToPlaylist', () => {
    it('should add song to playlist successfully', async () => {
      mockStorage.addSongToPlaylist.mockReturnValue(true)

      const playlist = usePlaylist()
      const result = await playlist.addSongToPlaylist('playlist-id', 'song-id')

      expect(result).toBe(true)
      expect(mockStorage.addSongToPlaylist).toHaveBeenCalledWith('playlist-id', 'song-id')
    })

    it('should handle add song failure', async () => {
      mockStorage.addSongToPlaylist.mockReturnValue(false)

      const playlist = usePlaylist()
      const result = await playlist.addSongToPlaylist('playlist-id', 'song-id')

      expect(result).toBe(false)
      expect(playlist.error.value).toBe('Failed to add song to playlist')
    })
  })

  describe('utility functions', () => {
    it('should convert SongItem to PlaylistSong', () => {
      const playlist = usePlaylist()

      const songItem = {
        id: 'song-1',
        title: 'Test Song',
        artist: 'Test Artist',
        artist_slug: 'test-artist',
        thumbnail: 'test.jpg',
        key: 'C',
        chords: ['C', 'G', 'Am', 'F'],
        genre: 'pop',
        genreThai: 'ป๊อป',
        difficulty: 'easy',
        difficultyThai: 'ง่าย',
        tuning: 'standard',
        tempo: 120,
        timeSignature: '4/4',
        capo: 0,
        strummingPattern: 'DUDUD',
        lyrics: [],
        tags: [],
        description: 'Test song',
        youtubeId: 'test-youtube-id',
      }

      const result = playlist.songToPlaylistSong(songItem)

      expect(result).toEqual({
        id: 'song-1',
        title: 'Test Song',
        artist: 'Test Artist',
        artist_slug: 'test-artist',
        thumbnail: 'test.jpg',
        key: 'C',
        chords: ['C', 'G', 'Am', 'F'],
      })
    })

    it('should generate share URL correctly', () => {
      // Mock window.location
      Object.defineProperty(global, 'window', {
        value: {
          location: {
            origin: 'https://chords.mhalong.com',
          },
        },
        writable: true,
      })

      const playlist = usePlaylist()

      const playlistItem = {
        id: 'test-id',
        name: 'My Playlist',
        songIds: ['song-1', 'song-2', 'song-3'],
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      }

      const result = playlist.generateShareUrl(playlistItem)

      expect(result).toBe('https://chords.mhalong.com/playlists/My%20Playlist?ids=song-1,song-2,song-3')
    })

    it('should parse shared playlist correctly', () => {
      const playlist = usePlaylist()

      const result = playlist.parseSharedPlaylist('My%20Playlist', 'song-1,song-2,song-3')

      expect(result).toEqual({
        name: 'My Playlist',
        songIds: ['song-1', 'song-2', 'song-3'],
      })
    })

    it('should return null for invalid shared playlist data', () => {
      const playlist = usePlaylist()

      const result = playlist.parseSharedPlaylist('', '')

      expect(result).toBeNull()
    })
  })

  describe('export/import', () => {
    it('should export playlists', () => {
      const exportData = '{"version":"1.0.0","playlists":[]}'

      mockStorage.exportPlaylists.mockReturnValue(exportData)

      const playlist = usePlaylist()
      const result = playlist.exportPlaylists()

      expect(result).toBe(exportData)
      expect(mockStorage.exportPlaylists).toHaveBeenCalled()
    })

    it('should import playlists successfully', async () => {
      mockStorage.importPlaylists.mockReturnValue(true)

      const playlist = usePlaylist()
      const result = await playlist.importPlaylists('{"version":"1.0.0","playlists":[]}', false)

      expect(result).toBe(true)
      expect(mockStorage.importPlaylists).toHaveBeenCalledWith('{"version":"1.0.0","playlists":[]}', false)
    })

    it('should handle import failure', async () => {
      mockStorage.importPlaylists.mockReturnValue(false)

      const playlist = usePlaylist()
      const result = await playlist.importPlaylists('invalid data', false)

      expect(result).toBe(false)
      expect(playlist.error.value).toBe('Failed to import playlists')
    })
  })
})
