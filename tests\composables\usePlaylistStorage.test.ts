import { describe, it, expect, beforeEach, vi } from 'vitest'
import { usePlaylistStorage } from '~/composables/usePlaylistStorage'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

// Mock window and localStorage
Object.defineProperty(global, 'window', {
  value: {
    localStorage: localStorageMock,
  },
  writable: true,
})

// Also mock for direct access
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

describe('usePlaylistStorage', () => {
  const storage = usePlaylistStorage()

  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockImplementation(() => {})
    localStorageMock.removeItem.mockImplementation(() => {})
  })

  describe('isStorageAvailable', () => {
    it('should return true when localStorage is available', () => {
      expect(storage.isStorageAvailable()).toBe(true)
    })

    it('should return false when localStorage throws error', () => {
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('Storage not available')
      })

      expect(storage.isStorageAvailable()).toBe(false)
    })
  })

  describe('loadPlaylists', () => {
    it('should return default data when no stored data exists', () => {
      const result = storage.loadPlaylists()

      expect(result).toEqual({
        version: '1.0.0',
        playlists: [],
        lastUpdated: expect.any(String),
      })
    })

    it('should load and validate stored playlists', () => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'test-1',
            name: 'Test Playlist',
            songIds: ['song-1', 'song-2'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))

      const result = storage.loadPlaylists()

      expect(result).toEqual(mockData)
    })

    it('should filter out invalid playlists', () => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'valid-playlist',
            name: 'Valid Playlist',
            songIds: ['song-1'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
          {
            // Invalid playlist - missing required fields
            id: 'invalid-playlist',
            name: 'Invalid Playlist',
            // missing songIds, createdAt, updatedAt
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))

      const result = storage.loadPlaylists()

      expect(result.playlists).toHaveLength(1)
      expect(result.playlists[0].id).toBe('valid-playlist')
    })
  })

  describe('createPlaylist', () => {
    it('should create a new playlist successfully', () => {
      const result = storage.createPlaylist('My New Playlist')

      expect(result).toEqual({
        id: expect.stringMatching(/^playlist_\d+_[a-z0-9]+$/),
        name: 'My New Playlist',
        songIds: [],
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      })

      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    it('should not create playlist with empty name', () => {
      const result = storage.createPlaylist('')

      expect(result).toBeNull()
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })

    it('should not create playlist with duplicate name', () => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'existing-playlist',
            name: 'Existing Playlist',
            songIds: [],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))

      const result = storage.createPlaylist('Existing Playlist')

      expect(result).toBeNull()
    })
  })

  describe('addSongToPlaylist', () => {
    beforeEach(() => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'test-playlist',
            name: 'Test Playlist',
            songIds: ['existing-song'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))
    })

    it('should add song to playlist successfully', () => {
      const result = storage.addSongToPlaylist('test-playlist', 'new-song')

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    it('should not add duplicate song to playlist', () => {
      const result = storage.addSongToPlaylist('test-playlist', 'existing-song')

      expect(result).toBe(false)
    })

    it('should return false for non-existent playlist', () => {
      const result = storage.addSongToPlaylist('non-existent', 'song-id')

      expect(result).toBe(false)
    })
  })

  describe('reorderPlaylistSongs', () => {
    beforeEach(() => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'test-playlist',
            name: 'Test Playlist',
            songIds: ['song-1', 'song-2', 'song-3'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))
    })

    it('should reorder songs successfully', () => {
      const newOrder = ['song-3', 'song-1', 'song-2']
      const result = storage.reorderPlaylistSongs('test-playlist', newOrder)

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    it('should reject invalid song order', () => {
      const invalidOrder = ['song-1', 'song-4'] // song-4 doesn't exist, missing song-2 and song-3
      const result = storage.reorderPlaylistSongs('test-playlist', invalidOrder)

      expect(result).toBe(false)
    })
  })

  describe('exportPlaylists', () => {
    it('should export playlists as JSON string', () => {
      const mockData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'test-playlist',
            name: 'Test Playlist',
            songIds: ['song-1'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData))

      const result = storage.exportPlaylists()
      const parsed = JSON.parse(result)

      expect(parsed).toEqual(mockData)
    })
  })

  describe('importPlaylists', () => {
    it('should import playlists successfully', () => {
      const importData = {
        version: '1.0.0',
        playlists: [
          {
            id: 'imported-playlist',
            name: 'Imported Playlist',
            songIds: ['song-1'],
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
          },
        ],
        lastUpdated: '2024-01-01T00:00:00.000Z',
      }

      const result = storage.importPlaylists(JSON.stringify(importData), false)

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalled()
    })

    it('should reject invalid import data', () => {
      const invalidData = {
        invalid: 'data',
      }

      const result = storage.importPlaylists(JSON.stringify(invalidData), false)

      expect(result).toBe(false)
    })

    it('should handle malformed JSON', () => {
      const result = storage.importPlaylists('invalid json', false)

      expect(result).toBe(false)
    })
  })

  describe('clearAllPlaylists', () => {
    it('should clear all playlists', () => {
      const result = storage.clearAllPlaylists()

      expect(result).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'mhalong_playlists',
        expect.stringContaining('"playlists":[]'),
      )
    })
  })
})
