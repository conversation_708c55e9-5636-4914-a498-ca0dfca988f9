import axios from 'axios'
import { DochordParser } from '~/utils/DochordParser'

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')

  try {
    const webContentResponse = await axios.get(`https://www.dochord.com/${id}/`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    })

    const webContent = webContentResponse.data as string

    const dochordParser = new DochordParser(webContent)

    return dochordParser.parse()
  } catch (error) {
    console.error('Error fetching song from dochord.com:', error)
    event.node.res.statusCode = 500

    return {
      code: '500',
      error: 'Failed to fetch song',
    }
  }
})
